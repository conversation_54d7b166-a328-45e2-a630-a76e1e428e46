#!/usr/bin/env python3
"""
WEEK 2 LIVE DEMO: Real-time LoG + 3D Spatial Intelligence
Watch the enhanced system with edge refinement and 3D mapping in action!
"""

import cv2
import numpy as np
import time
from src.main import PersonUnderLoadSystem

def realtime_week2_demo():
    """Live demo of Week 2 enhanced system with real-time 3D spatial intelligence"""
    
    video_path = "data/test_videos/test2.mp4"
    
    print("🚀 WEEK 2 LIVE DEMO: 3D Spatial Intelligence")
    print("=" * 80)
    print("🔬 ENHANCED FEATURES:")
    print("   🔍 LoG Edge Refinement - Precise object boundaries")
    print("   🌐 3D Spatial Mapping - Real-world coordinates")
    print("   📏 Distance Calculations - True 3D measurements")
    print("   🛡️ Enhanced Safety Assessment - Overhead danger zones")
    print("=" * 80)
    print("🎮 CONTROLS:")
    print("   SPACE - Pause/Resume")
    print("   'q' - Quit")
    print("   's' - Save current enhanced frame")
    print("   'r' - Reset to beginning")
    print("   'i' - Toggle info overlay")
    print("   '+/-' - Speed control")
    print("=" * 80)
    
    # Initialize enhanced system
    print("🚀 Initializing Week 2 enhanced system...")
    system = PersonUnderLoadSystem()
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"❌ Could not open video: {video_path}")
        return
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📊 Video: {width}x{height} @ {fps:.1f} FPS, {total_frames} frames ({total_frames/fps:.1f}s)")
    print(f"🚀 Starting enhanced real-time playback...")
    
    # Control variables
    paused = False
    speed_multiplier = 1.0
    frame_delay = 1.0 / fps
    saved_frame_count = 0
    show_info_overlay = True
    
    # Performance tracking
    processing_times = []
    refinement_stats = []
    spatial_stats = []
    display_fps = 0
    fps_update_time = time.time()
    fps_frame_count = 0
    
    # Real-time statistics
    live_stats = {
        'total_workers': 0,
        'total_loads': 0,
        'total_refined': 0,
        'total_3d_mapped': 0,
        'avg_edge_strength': 0,
        'avg_geometric_confidence': 0,
        'avg_3d_distance': 0,
        'frames_processed': 0,
    }
    
    while True:
        if not paused:
            ret, frame = cap.read()
            if not ret:
                print("📹 Video ended. Press 'r' to restart or 'q' to quit.")
                paused = True
                continue
        
        if not paused:
            # Process frame with enhanced system
            start_time = time.time()
            annotated_frame, stats = system.process_frame(frame)
            processing_time = time.time() - start_time
            processing_times.append(processing_time)
            
            # Get current frame position
            current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
            progress = (current_frame / total_frames) * 100
            
            # Extract enhanced stats
            detection_stats = stats['detection_stats']
            refinement_stats_frame = stats['refinement_stats']
            spatial_stats_frame = stats['spatial_stats']
            safety_status = stats['safety_status']
            
            # Update live statistics
            live_stats['frames_processed'] += 1
            live_stats['total_workers'] += safety_status['workers_detected']
            live_stats['total_loads'] += safety_status['loads_detected']
            live_stats['total_refined'] += refinement_stats_frame['refined_detections']
            live_stats['total_3d_mapped'] += spatial_stats_frame['3d_positions']
            
            # Running averages
            if refinement_stats_frame['avg_edge_strength'] > 0:
                live_stats['avg_edge_strength'] = (live_stats['avg_edge_strength'] * 0.9 + 
                                                 refinement_stats_frame['avg_edge_strength'] * 0.1)
            if refinement_stats_frame['avg_geometric_confidence'] > 0:
                live_stats['avg_geometric_confidence'] = (live_stats['avg_geometric_confidence'] * 0.9 + 
                                                        refinement_stats_frame['avg_geometric_confidence'] * 0.1)
            if spatial_stats_frame['avg_distance_from_camera'] > 0:
                live_stats['avg_3d_distance'] = (live_stats['avg_3d_distance'] * 0.9 + 
                                                spatial_stats_frame['avg_distance_from_camera'] * 0.1)
            
            # Add enhanced performance overlay
            if show_info_overlay:
                # Main performance info
                perf_text = f"Frame: {current_frame}/{total_frames} ({progress:.1f}%) | Processing: {processing_time*1000:.0f}ms | FPS: {display_fps:.1f}"
                cv2.putText(annotated_frame, perf_text, (10, height - 120), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(annotated_frame, perf_text, (10, height - 120), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
                
                # Week 2 enhancement info
                enhancement_text = f"LoG Refined: {refinement_stats_frame['refined_detections']} | 3D Mapped: {spatial_stats_frame['3d_positions']} | Edge: {refinement_stats_frame['avg_edge_strength']:.0f}"
                cv2.putText(annotated_frame, enhancement_text, (10, height - 95), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                cv2.putText(annotated_frame, enhancement_text, (10, height - 95), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
                
                # 3D spatial info
                spatial_text = f"Geo Conf: {refinement_stats_frame['avg_geometric_confidence']:.3f} | Avg Dist: {spatial_stats_frame['avg_distance_from_camera']:.1f}m | 3D Analysis: ACTIVE"
                cv2.putText(annotated_frame, spatial_text, (10, height - 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 255), 2)
                cv2.putText(annotated_frame, spatial_text, (10, height - 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
                
                # Live cumulative stats
                cumulative_text = f"Total: W:{live_stats['total_workers']} L:{live_stats['total_loads']} | Refined: {live_stats['total_refined']} | 3D: {live_stats['total_3d_mapped']}"
                cv2.putText(annotated_frame, cumulative_text, (10, height - 45), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                cv2.putText(annotated_frame, cumulative_text, (10, height - 45), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
            
            # Control info
            control_text = f"Speed: {speed_multiplier:.1f}x | SPACE=Pause, Q=Quit, S=Save, I=Info, +/-=Speed"
            cv2.putText(annotated_frame, control_text, (10, height - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
            cv2.putText(annotated_frame, control_text, (10, height - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            
            # Calculate display FPS
            fps_frame_count += 1
            if time.time() - fps_update_time >= 1.0:
                display_fps = fps_frame_count / (time.time() - fps_update_time)
                fps_update_time = time.time()
                fps_frame_count = 0
                
                # Print live stats to console
                print(f"🔴 LIVE: Frame {current_frame} | Workers: {safety_status['workers_detected']} | "
                      f"Loads: {safety_status['loads_detected']} | Refined: {refinement_stats_frame['refined_detections']} | "
                      f"3D: {spatial_stats_frame['3d_positions']} | Status: {safety_status['overall_status']} | "
                      f"Processing: {processing_time*1000:.0f}ms")
        
        else:
            # Show paused frame
            if 'annotated_frame' in locals():
                pause_overlay = annotated_frame.copy()
                cv2.rectangle(pause_overlay, (width//2 - 150, height//2 - 50), 
                             (width//2 + 150, height//2 + 50), (0, 0, 0), -1)
                cv2.putText(pause_overlay, "PAUSED - WEEK 2 ENHANCED", (width//2 - 140, height//2 + 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 255), 3)
                annotated_frame = pause_overlay
            else:
                annotated_frame = np.zeros((height, width, 3), dtype=np.uint8)
                cv2.putText(annotated_frame, "Video Ended - Press 'r' to restart", 
                           (width//2 - 200, height//2), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Display enhanced frame
        cv2.imshow('Week 2 Enhanced: LoG + 3D Spatial Intelligence', annotated_frame)
        
        # Handle keyboard input
        key = cv2.waitKey(int(frame_delay * 1000 / speed_multiplier)) & 0xFF
        
        if key == ord('q'):
            print("👋 Quitting enhanced demo...")
            break
        elif key == ord(' '):
            paused = not paused
            status = "⏸️  PAUSED" if paused else "▶️  RESUMED"
            print(f"{status} - Week 2 Enhanced Demo")
        elif key == ord('s'):
            if 'annotated_frame' in locals():
                saved_frame_count += 1
                filename = f"week2_live_capture_{saved_frame_count:03d}.jpg"
                cv2.imwrite(filename, annotated_frame)
                print(f"💾 Saved enhanced frame: {filename}")
        elif key == ord('r'):
            print("🔄 Restarting enhanced demo...")
            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            paused = False
            # Reset live stats
            live_stats = {k: 0 for k in live_stats.keys()}
        elif key == ord('i'):
            show_info_overlay = not show_info_overlay
            print(f"ℹ️  Info overlay: {'ON' if show_info_overlay else 'OFF'}")
        elif key == ord('+') or key == ord('='):
            speed_multiplier = min(speed_multiplier * 1.5, 5.0)
            print(f"⚡ Speed: {speed_multiplier:.1f}x")
        elif key == ord('-') or key == ord('_'):
            speed_multiplier = max(speed_multiplier / 1.5, 0.25)
            print(f"🐌 Speed: {speed_multiplier:.1f}x")
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    
    # Final enhanced statistics
    if processing_times:
        avg_processing = np.mean(processing_times)
        
        print(f"\n📊 WEEK 2 ENHANCED DEMO STATISTICS:")
        print(f"=" * 60)
        print(f"⚡ PERFORMANCE:")
        print(f"   - Frames processed: {live_stats['frames_processed']}")
        print(f"   - Average processing time: {avg_processing*1000:.1f}ms")
        print(f"   - Theoretical max FPS: {1/avg_processing:.1f}")
        
        print(f"\n🔍 LoG EDGE REFINEMENT:")
        print(f"   - Total objects refined: {live_stats['total_refined']}")
        print(f"   - Average edge strength: {live_stats['avg_edge_strength']:.1f}")
        print(f"   - Average geometric confidence: {live_stats['avg_geometric_confidence']:.3f}")
        
        print(f"\n🌐 3D SPATIAL MAPPING:")
        print(f"   - Total 3D positions: {live_stats['total_3d_mapped']}")
        print(f"   - Average distance: {live_stats['avg_3d_distance']:.1f}m")
        
        print(f"\n🎯 DETECTION SUMMARY:")
        print(f"   - Total workers detected: {live_stats['total_workers']}")
        print(f"   - Total loads detected: {live_stats['total_loads']}")
        print(f"   - Frames saved: {saved_frame_count}")
        
        print(f"\n🎉 Week 2 Enhanced Demo Completed!")
        print(f"🛡️ 3D Spatial Intelligence is OPERATIONAL and ready to save lives!")

if __name__ == "__main__":
    realtime_week2_demo()
