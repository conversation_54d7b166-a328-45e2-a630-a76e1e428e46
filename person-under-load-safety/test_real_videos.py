#!/usr/bin/env python3
"""
Real video testing script for the Person-Under-Load Safety System
Tests with actual construction site footage
"""

import cv2
import numpy as np
import os
import time
from src.main import PersonUnderLoadSystem

def test_video(video_path: str, max_frames: int = 100, save_output: bool = True):
    """
    Test the system with a real video file
    
    Args:
        video_path: Path to the video file
        max_frames: Maximum number of frames to process
        save_output: Whether to save annotated video output
    """
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        return False
    
    print(f"🎬 Testing with video: {os.path.basename(video_path)}")
    print(f"📁 Full path: {video_path}")
    
    # Initialize system
    system = PersonUnderLoadSystem()
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"❌ Could not open video: {video_path}")
        return False
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📊 Video properties:")
    print(f"   - Resolution: {width}x{height}")
    print(f"   - FPS: {fps}")
    print(f"   - Total frames: {total_frames}")
    print(f"   - Duration: {total_frames/fps:.1f} seconds")
    
    # Setup output video if saving
    output_path = None
    out = None
    if save_output:
        output_filename = f"output_{os.path.splitext(os.path.basename(video_path))[0]}.mp4"
        output_path = os.path.join("data", "test_videos", output_filename)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        print(f"💾 Will save output to: {output_path}")
    
    # Processing statistics
    frame_count = 0
    total_workers = 0
    total_loads = 0
    total_warnings = 0
    processing_times = []
    
    # Detection statistics
    detection_results = {
        'frames_with_workers': 0,
        'frames_with_loads': 0,
        'frames_with_warnings': 0,
        'max_workers_in_frame': 0,
        'max_loads_in_frame': 0,
    }
    
    print(f"\n🔍 Processing frames (max {max_frames})...")
    print("=" * 60)
    
    start_time = time.time()
    
    while frame_count < max_frames:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_start = time.time()
        
        # Process frame through our system
        annotated_frame, stats = system.process_frame(frame)
        
        frame_time = time.time() - frame_start
        processing_times.append(frame_time)
        
        # Extract statistics
        safety_status = stats['safety_status']
        workers = safety_status['workers_detected']
        loads = safety_status['loads_detected']
        warnings = len(safety_status['warnings'])
        
        # Update counters
        total_workers += workers
        total_loads += loads
        total_warnings += warnings
        
        # Update detection statistics
        if workers > 0:
            detection_results['frames_with_workers'] += 1
            detection_results['max_workers_in_frame'] = max(detection_results['max_workers_in_frame'], workers)
        
        if loads > 0:
            detection_results['frames_with_loads'] += 1
            detection_results['max_loads_in_frame'] = max(detection_results['max_loads_in_frame'], loads)
        
        if warnings > 0:
            detection_results['frames_with_warnings'] += 1
        
        # Save frame if output video is enabled
        if out is not None:
            out.write(annotated_frame)
        
        # Print progress every 10 frames
        if frame_count % 10 == 0:
            status = safety_status['overall_status']
            print(f"Frame {frame_count:3d}: Workers={workers}, Loads={loads}, Warnings={warnings}, Status={status}, Time={frame_time:.3f}s")
        
        frame_count += 1
    
    total_time = time.time() - start_time
    
    # Cleanup
    cap.release()
    if out is not None:
        out.release()
    
    # Print comprehensive results
    print("=" * 60)
    print(f"✅ Video processing completed!")
    print(f"\n📊 PROCESSING STATISTICS:")
    print(f"   - Frames processed: {frame_count}")
    print(f"   - Total processing time: {total_time:.2f} seconds")
    print(f"   - Average FPS: {frame_count / total_time:.2f}")
    print(f"   - Average frame time: {np.mean(processing_times):.3f} seconds")
    print(f"   - Min frame time: {np.min(processing_times):.3f} seconds")
    print(f"   - Max frame time: {np.max(processing_times):.3f} seconds")
    
    print(f"\n🔍 DETECTION RESULTS:")
    print(f"   - Frames with workers: {detection_results['frames_with_workers']}/{frame_count} ({detection_results['frames_with_workers']/frame_count*100:.1f}%)")
    print(f"   - Frames with loads: {detection_results['frames_with_loads']}/{frame_count} ({detection_results['frames_with_loads']/frame_count*100:.1f}%)")
    print(f"   - Frames with warnings: {detection_results['frames_with_warnings']}/{frame_count} ({detection_results['frames_with_warnings']/frame_count*100:.1f}%)")
    print(f"   - Max workers in single frame: {detection_results['max_workers_in_frame']}")
    print(f"   - Max loads in single frame: {detection_results['max_loads_in_frame']}")
    print(f"   - Total worker detections: {total_workers}")
    print(f"   - Total load detections: {total_loads}")
    print(f"   - Total warnings issued: {total_warnings}")
    
    if save_output and output_path:
        print(f"\n💾 OUTPUT SAVED:")
        print(f"   - Annotated video: {output_path}")
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)  # MB
            print(f"   - File size: {file_size:.1f} MB")
    
    return True

def main():
    """Test both videos"""
    
    print("🚀 REAL VIDEO TESTING - Person-Under-Load Safety System")
    print("=" * 70)
    
    # Test video paths
    video1 = "data/test_videos/nachine-human.mp4"  # Humans only
    video2 = "data/test_videos/test2.mp4"          # Machine and human
    
    # Test Video 1: Humans only
    print("\n🎬 TEST 1: HUMANS ONLY VIDEO")
    print("-" * 40)
    success1 = test_video(video1, max_frames=50, save_output=True)
    
    print("\n" + "="*70)
    
    # Test Video 2: Machine and human
    print("\n🎬 TEST 2: MACHINE AND HUMAN VIDEO")
    print("-" * 40)
    success2 = test_video(video2, max_frames=50, save_output=True)
    
    # Final summary
    print("\n" + "="*70)
    print("📋 FINAL TEST SUMMARY:")
    print(f"   - Video 1 (humans only): {'✅ SUCCESS' if success1 else '❌ FAILED'}")
    print(f"   - Video 2 (machine+human): {'✅ SUCCESS' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        print("\n🎉 ALL REAL VIDEO TESTS PASSED!")
        print("📁 Check the output videos in data/test_videos/")
        print("🚀 System is validated with real construction site footage!")
    else:
        print("\n⚠️  Some tests failed. Check error messages above.")

if __name__ == "__main__":
    main()
