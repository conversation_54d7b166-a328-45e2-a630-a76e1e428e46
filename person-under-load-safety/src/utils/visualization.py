import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
from src.detection.object_tracker import TrackedObject
from src.geometry.edge_refinement import RefinedDetection
from src.geometry.coordinate_mapper import Position3D

class SafetyVisualizer:
    """Visualization utilities for the safety system"""
    
    def __init__(self):
        # Color scheme for different object types - IMPROVED READABILITY
        self.colors = {
            'person': (0, 255, 255),      # Bright Cyan for workers (more visible)
            'crane': (0, 165, 255),       # Orange for cranes
            'suspended_load': (0, 0, 255), # Red for suspended loads
            'truck': (255, 255, 0),       # Yellow for trucks
            'car': (255, 0, 255),         # Magenta for cars
            'default': (128, 128, 128),   # Gray for unknown
        }
        
        # Safety status colors
        self.safety_colors = {
            'SAFE': (0, 255, 0),      # Green
            'MEDIUM': (0, 255, 255),  # Yellow
            'HIGH': (0, 165, 255),    # Orange
            'DANGER': (0, 0, 255),    # Red
        }
        
    def draw_detections(self, frame: np.ndarray, tracked_objects: List[TrackedObject],
                       safety_status: Dict) -> np.ndarray:
        """Legacy method for backward compatibility"""
        return self.draw_enhanced_detections(frame, tracked_objects, None, None, safety_status)

    def draw_enhanced_detections(self, frame: np.ndarray, tracked_objects: List[TrackedObject],
                               refined_detections: Optional[List[RefinedDetection]],
                               positions_3d: Optional[List[Position3D]],
                               safety_status: Dict) -> np.ndarray:
        """
        Draw enhanced bounding boxes and 3D spatial information on frame

        Args:
            frame: Input frame
            tracked_objects: List of tracked objects
            refined_detections: LoG-refined detections (optional)
            positions_3d: 3D spatial positions (optional)
            safety_status: Safety assessment results

        Returns:
            Annotated frame with enhanced 3D information
        """
        annotated_frame = frame.copy()

        # Draw enhanced object bounding boxes with 3D info
        for i, obj in enumerate(tracked_objects):
            refined_det = refined_detections[i] if refined_detections and i < len(refined_detections) else None
            pos_3d = positions_3d[i] if positions_3d and i < len(positions_3d) else None
            self._draw_enhanced_object_box(annotated_frame, obj, refined_det, pos_3d)

        # Draw refined detection overlays
        if refined_detections:
            self._draw_refinement_overlays(annotated_frame, refined_detections)

        # Draw 3D spatial information
        if positions_3d:
            self._draw_3d_spatial_info(annotated_frame, positions_3d)

        # Draw safety warnings
        self._draw_safety_warnings(annotated_frame, safety_status)

        # Draw enhanced system status
        self._draw_enhanced_system_status(annotated_frame, safety_status, refined_detections, positions_3d)

        return annotated_frame
    
    def _draw_object_box(self, frame: np.ndarray, obj: TrackedObject):
        """Draw bounding box for a tracked object"""
        
        # Get color based on object type
        color = self.colors.get(obj.class_name, self.colors['default'])
        
        # Extract bbox coordinates
        x1, y1, x2, y2 = map(int, obj.bbox)
        
        # Draw bounding box
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
        
        # Prepare label text
        label = f"{obj.class_name} #{obj.id}"
        confidence_text = f"{obj.confidence:.2f}"
        
        # Calculate text size
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 1
        
        (label_w, label_h), _ = cv2.getTextSize(label, font, font_scale, thickness)
        (conf_w, conf_h), _ = cv2.getTextSize(confidence_text, font, font_scale, thickness)
        
        # Draw label background
        label_bg_y1 = max(y1 - label_h - conf_h - 10, 0)
        label_bg_y2 = y1
        cv2.rectangle(frame, (x1, label_bg_y1), (x1 + max(label_w, conf_w) + 10, label_bg_y2), color, -1)
        
        # Draw label text
        cv2.putText(frame, label, (x1 + 5, y1 - conf_h - 5), font, font_scale, (255, 255, 255), thickness)
        cv2.putText(frame, confidence_text, (x1 + 5, y1 - 5), font, font_scale, (255, 255, 255), thickness)
        
        # Draw velocity arrow if available
        if obj.velocity and (abs(obj.velocity[0]) > 1 or abs(obj.velocity[1]) > 1):
            center_x, center_y = map(int, obj.center)
            end_x = int(center_x + obj.velocity[0] * 10)  # Scale velocity for visibility
            end_y = int(center_y + obj.velocity[1] * 10)
            cv2.arrowedLine(frame, (center_x, center_y), (end_x, end_y), color, 2)
    
    def _draw_safety_warnings(self, frame: np.ndarray, safety_status: Dict):
        """Draw safety warnings on frame"""
        
        warnings = safety_status.get('warnings', [])
        
        for i, warning in enumerate(warnings):
            risk_level = warning['risk_level']
            color = self.safety_colors.get(risk_level, self.safety_colors['DANGER'])
            
            # Warning text
            warning_text = f"WARNING: Worker #{warning['worker_id']} near Load #{warning['load_id']}"
            # Handle both old 2D distance and new 3D distance formats
            if 'distance_3d' in warning:
                distance_text = f"3D Distance: {warning['distance_3d']:.1f}m"
            elif 'horizontal_distance' in warning:
                distance_text = f"Horizontal: {warning['horizontal_distance']:.1f}m"
            elif 'distance' in warning:
                distance_text = f"Distance: {warning['distance']:.1f}px"
            else:
                distance_text = "Distance: N/A"
            
            # Position warning text
            y_pos = 30 + i * 50
            
            # Draw warning background
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.7
            thickness = 2
            
            (w_w, w_h), _ = cv2.getTextSize(warning_text, font, font_scale, thickness)
            (d_w, d_h), _ = cv2.getTextSize(distance_text, font, font_scale, thickness)
            
            bg_width = max(w_w, d_w) + 20
            bg_height = w_h + d_h + 20
            
            cv2.rectangle(frame, (10, y_pos - 10), (10 + bg_width, y_pos + bg_height), color, -1)
            cv2.rectangle(frame, (10, y_pos - 10), (10 + bg_width, y_pos + bg_height), (0, 0, 0), 2)
            
            # Draw warning text
            cv2.putText(frame, warning_text, (20, y_pos + w_h), font, font_scale, (255, 255, 255), thickness)
            cv2.putText(frame, distance_text, (20, y_pos + w_h + d_h + 5), font, font_scale, (255, 255, 255), thickness)
    
    def _draw_system_status(self, frame: np.ndarray, safety_status: Dict):
        """Draw overall system status"""
        
        height, width = frame.shape[:2]
        
        # Overall status
        overall_status = safety_status.get('overall_status', 'UNKNOWN')
        status_color = self.safety_colors.get(overall_status, self.safety_colors['DANGER'])
        
        # Status text
        status_text = f"SYSTEM STATUS: {overall_status}"
        workers_text = f"Workers: {safety_status.get('workers_detected', 0)}"
        loads_text = f"Loads: {safety_status.get('loads_detected', 0)}"
        
        # Position in top-right corner
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.8
        thickness = 2
        
        (s_w, s_h), _ = cv2.getTextSize(status_text, font, font_scale, thickness)
        (w_w, w_h), _ = cv2.getTextSize(workers_text, font, font_scale, thickness)
        (l_w, l_h), _ = cv2.getTextSize(loads_text, font, font_scale, thickness)
        
        bg_width = max(s_w, w_w, l_w) + 20
        bg_height = s_h + w_h + l_h + 30
        
        # Draw status background
        bg_x = width - bg_width - 10
        bg_y = 10
        
        cv2.rectangle(frame, (bg_x, bg_y), (bg_x + bg_width, bg_y + bg_height), status_color, -1)
        cv2.rectangle(frame, (bg_x, bg_y), (bg_x + bg_width, bg_y + bg_height), (0, 0, 0), 2)
        
        # Draw status text
        text_x = bg_x + 10
        cv2.putText(frame, status_text, (text_x, bg_y + s_h + 10), font, font_scale, (255, 255, 255), thickness)
        cv2.putText(frame, workers_text, (text_x, bg_y + s_h + w_h + 15), font, font_scale, (255, 255, 255), thickness)
        cv2.putText(frame, loads_text, (text_x, bg_y + s_h + w_h + l_h + 20), font, font_scale, (255, 255, 255), thickness)
    
    def draw_trajectory(self, frame: np.ndarray, obj: TrackedObject, max_points: int = 30):
        """Draw trajectory trail for an object"""
        
        if not obj.trajectory or len(obj.trajectory) < 2:
            return
        
        color = self.colors.get(obj.class_name, self.colors['default'])
        
        # Draw trajectory points
        points = list(obj.trajectory)[-max_points:]  # Last N points
        
        for i in range(1, len(points)):
            pt1 = tuple(map(int, points[i-1][0]))  # Previous position
            pt2 = tuple(map(int, points[i][0]))    # Current position
            
            # Fade older points
            alpha = i / len(points)
            line_color = tuple(int(c * alpha) for c in color)
            
            cv2.line(frame, pt1, pt2, line_color, 2)

    def _draw_enhanced_object_box(self, frame: np.ndarray, obj: TrackedObject,
                                 refined_det: Optional[RefinedDetection] = None,
                                 pos_3d: Optional[Position3D] = None):
        """Draw enhanced bounding box with 3D information"""

        # Get color based on object type
        color = self.colors.get(obj.class_name, self.colors['default'])

        # Use refined bbox if available, otherwise original
        if refined_det:
            x1, y1, x2, y2 = map(int, refined_det.refined_bbox)
            # Draw original bbox in lighter color for comparison
            orig_x1, orig_y1, orig_x2, orig_y2 = map(int, obj.bbox)
            cv2.rectangle(frame, (orig_x1, orig_y1), (orig_x2, orig_y2),
                         tuple(c//2 for c in color), 1)  # Dimmed original
        else:
            x1, y1, x2, y2 = map(int, obj.bbox)

        # Draw main bounding box (refined or original) - THICKER FOR VISIBILITY
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 3)

        # Prepare enhanced label text - IMPROVED ID VISIBILITY
        label_parts = [f"{obj.class_name} ID:{obj.id:02d}"]

        if refined_det:
            label_parts.append(f"Edge:{refined_det.edge_strength:.0f}")
            label_parts.append(f"Geo:{refined_det.geometric_confidence:.2f}")

        if pos_3d:
            distance = np.sqrt(pos_3d.x**2 + pos_3d.y**2 + pos_3d.z**2)
            label_parts.append(f"Dist:{distance:.1f}m")
            label_parts.append(f"3D:({pos_3d.x:.1f},{pos_3d.y:.1f},{pos_3d.z:.1f})")

        # Draw multi-line label - IMPROVED READABILITY
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6  # Larger font
        thickness = 2     # Thicker text
        line_height = 20  # More spacing

        for i, text in enumerate(label_parts):
            y_offset = y1 - (len(label_parts) - i) * line_height
            y_offset = max(line_height, y_offset)  # Keep on screen

            # Draw text background with better contrast
            (text_w, text_h), _ = cv2.getTextSize(text, font, font_scale, thickness)
            # Black background for maximum contrast
            cv2.rectangle(frame, (x1, y_offset - text_h - 4),
                         (x1 + text_w + 8, y_offset + 4), (0, 0, 0), -1)
            # Colored border
            cv2.rectangle(frame, (x1, y_offset - text_h - 4),
                         (x1 + text_w + 8, y_offset + 4), color, 2)

            # Draw white text on black background for maximum readability
            cv2.putText(frame, text, (x1 + 4, y_offset), font, font_scale,
                       (255, 255, 255), thickness)

        # Draw velocity arrow if available
        if obj.velocity and (abs(obj.velocity[0]) > 1 or abs(obj.velocity[1]) > 1):
            center_x, center_y = map(int, obj.center)
            end_x = int(center_x + obj.velocity[0] * 10)
            end_y = int(center_y + obj.velocity[1] * 10)
            cv2.arrowedLine(frame, (center_x, center_y), (end_x, end_y), color, 2)

    def _draw_refinement_overlays(self, frame: np.ndarray, refined_detections: List[RefinedDetection]):
        """Draw LoG refinement visualization overlays - ONLY WHEN ACTUALLY REFINED"""

        for detection in refined_detections:
            # Only draw contours if they actually exist (not in speed mode)
            if (detection.contour_points is not None and
                len(detection.contour_points) > 0 and
                detection.edge_strength != 100.0):  # Skip fake/default contours from speed mode

                # Draw contour outline only - no filled areas to avoid clutter
                cv2.drawContours(frame, [detection.contour_points], -1, (0, 255, 255), 1)

            # Only draw refined center if it actually exists (not in speed mode)
            if (detection.center_refined and
                detection.edge_strength != 100.0):  # Skip fake/default centers from speed mode

                # Draw refined center point - smaller and less intrusive
                center = tuple(map(int, detection.center_refined))
                cv2.circle(frame, center, 3, (0, 255, 255), -1)
                cv2.circle(frame, center, 5, (255, 255, 255), 1)  # Thin white border

    def _draw_3d_spatial_info(self, frame: np.ndarray, positions_3d: List[Position3D]):
        """Draw 3D spatial information overlay"""

        height, width = frame.shape[:2]

        # Draw 3D coordinate system indicator
        origin = (50, height - 100)
        scale = 30

        # X-axis (red)
        cv2.arrowedLine(frame, origin, (origin[0] + scale, origin[1]), (0, 0, 255), 2)
        cv2.putText(frame, "X", (origin[0] + scale + 5, origin[1] + 5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        # Y-axis (green)
        cv2.arrowedLine(frame, origin, (origin[0], origin[1] - scale), (0, 255, 0), 2)
        cv2.putText(frame, "Y", (origin[0] - 10, origin[1] - scale - 5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # Z-axis (blue) - diagonal to show depth
        cv2.arrowedLine(frame, origin, (origin[0] - scale//2, origin[1] - scale//2), (255, 0, 0), 2)
        cv2.putText(frame, "Z", (origin[0] - scale//2 - 15, origin[1] - scale//2),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

    def _draw_enhanced_system_status(self, frame: np.ndarray, safety_status: Dict,
                                   refined_detections: Optional[List[RefinedDetection]],
                                   positions_3d: Optional[List[Position3D]]):
        """Draw enhanced system status with 3D information"""

        height, width = frame.shape[:2]

        # Overall status
        overall_status = safety_status.get('overall_status', 'UNKNOWN')
        status_color = self.safety_colors.get(overall_status, self.safety_colors['DANGER'])

        # Enhanced status text
        status_lines = [
            f"SYSTEM STATUS: {overall_status}",
            f"Workers: {safety_status.get('workers_detected', 0)}",
            f"Loads: {safety_status.get('loads_detected', 0)}",
        ]

        if refined_detections:
            avg_edge_strength = np.mean([d.edge_strength for d in refined_detections]) if refined_detections else 0
            status_lines.append(f"LoG Refined: {len(refined_detections)}")
            status_lines.append(f"Avg Edge: {avg_edge_strength:.0f}")

        if positions_3d:
            avg_distance = np.mean([np.sqrt(p.x**2 + p.y**2 + p.z**2) for p in positions_3d]) if positions_3d else 0
            status_lines.append(f"3D Mapped: {len(positions_3d)}")
            status_lines.append(f"Avg Dist: {avg_distance:.1f}m")

        if safety_status.get('3d_analysis_active'):
            status_lines.append("3D ANALYSIS: ACTIVE")

        # Calculate background size
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 1

        max_width = 0
        total_height = 0
        line_heights = []

        for line in status_lines:
            (w, h), _ = cv2.getTextSize(line, font, font_scale, thickness)
            max_width = max(max_width, w)
            line_heights.append(h)
            total_height += h + 5

        # Position in top-right corner
        bg_width = max_width + 20
        bg_height = total_height + 20
        bg_x = width - bg_width - 10
        bg_y = 10

        # Draw status background
        cv2.rectangle(frame, (bg_x, bg_y), (bg_x + bg_width, bg_y + bg_height), status_color, -1)
        cv2.rectangle(frame, (bg_x, bg_y), (bg_x + bg_width, bg_y + bg_height), (0, 0, 0), 2)

        # Draw status text
        text_x = bg_x + 10
        current_y = bg_y + 15

        for i, line in enumerate(status_lines):
            cv2.putText(frame, line, (text_x, current_y), font, font_scale, (255, 255, 255), thickness)
            current_y += line_heights[i] + 5
