#!/usr/bin/env python3
"""
Week 3 Performance Benchmarking Tool
Identifies bottlenecks and optimizes for full-speed video playback

Measures:
- Individual component processing times
- Memory usage
- FPS capability
- Optimization opportunities
"""

import cv2
import numpy as np
import time
import sys
import psutil
import gc
from pathlib import Path
from typing import Dict, List

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.main import PersonUnderLoadSystem
from src.detection.yolo_detector import ConstructionSiteDetector
from src.detection.object_tracker import SafetyTracker
from src.geometry.edge_refinement import LaplacianEdgeRefiner
from src.geometry.enhanced_spatial_mapper import EnhancedSpatialMapper
from src.geometry.coordinate_mapper import CameraCalibration

class Week3Benchmarker:
    """Comprehensive performance benchmarking for Week 3 system"""
    
    def __init__(self):
        """Initialize benchmarker"""
        print("🔬 Week 3 Performance Benchmarker")
        print("=" * 50)
        
        # Initialize system components individually for detailed timing
        self.detector = ConstructionSiteDetector()
        self.tracker = SafetyTracker()
        self.edge_refiner = LaplacianEdgeRefiner(sigma=1.2, threshold=0.1)
        
        # Camera calibration
        camera_cal = CameraCalibration(
            focal_length=800,
            principal_point=(960, 540),
            image_size=(1920, 1080),
            height_above_ground=5.0,
            tilt_angle=np.radians(15)
        )
        self.spatial_mapper = EnhancedSpatialMapper(camera_cal)
        
        # Timing storage
        self.timings = {
            'yolo_detection': [],
            'object_tracking': [],
            'edge_refinement': [],
            'spatial_mapping': [],
            'visualization': [],
            'total_processing': []
        }
        
        # Memory tracking
        self.memory_usage = []
        
        print("✅ Benchmarker initialized")
    
    def benchmark_full_pipeline(self, video_path: str, num_frames: int = 50):
        """Benchmark the complete processing pipeline"""
        print(f"\n🎬 Benchmarking full pipeline with {num_frames} frames...")
        print("=" * 50)
        
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Error: Could not open video {video_path}")
            return
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        print(f"📹 Video FPS: {fps}")
        print(f"🎯 Target: Process {num_frames} frames as fast as possible")
        
        overall_start = time.time()
        
        for frame_idx in range(num_frames):
            # Read frame
            ret, frame = cap.read()
            if not ret:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Restart video
                ret, frame = cap.read()
                if not ret:
                    break
            
            frame_start = time.time()
            
            # 1. YOLO Detection
            detection_start = time.time()
            detections = self.detector.detect_objects(frame)
            objects_info = self.detector.get_object_details(detections)
            detection_time = time.time() - detection_start
            self.timings['yolo_detection'].append(detection_time)
            
            # 2. Object Tracking
            tracking_start = time.time()
            tracked_objects = self.tracker.update(detections, objects_info)
            tracking_time = time.time() - tracking_start
            self.timings['object_tracking'].append(tracking_time)
            
            # 3. Edge Refinement
            refinement_start = time.time()
            refined_detections = self.edge_refiner.refine_detections(frame, detections, objects_info)
            refinement_time = time.time() - refinement_start
            self.timings['edge_refinement'].append(refinement_time)
            
            # 4. Spatial Mapping
            mapping_start = time.time()
            positions_3d = self.spatial_mapper.map_detections_to_3d_enhanced(refined_detections, frame)
            mapping_time = time.time() - mapping_start
            self.timings['spatial_mapping'].append(mapping_time)
            
            # 5. Visualization (simulated - just measure annotation time)
            viz_start = time.time()
            # Simulate drawing operations
            for obj in tracked_objects:
                cv2.rectangle(frame, (10, 10), (100, 50), (255, 255, 255), 2)
                cv2.putText(frame, f"ID:{obj.id}", (15, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            viz_time = time.time() - viz_start
            self.timings['visualization'].append(viz_time)
            
            # Total frame time
            total_frame_time = time.time() - frame_start
            self.timings['total_processing'].append(total_frame_time)
            
            # Memory usage
            memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
            self.memory_usage.append(memory_mb)
            
            # Progress indicator
            if (frame_idx + 1) % 10 == 0:
                current_fps = 1.0 / total_frame_time
                print(f"Frame {frame_idx + 1}/{num_frames} - {current_fps:.1f} FPS - {total_frame_time*1000:.1f}ms")
        
        cap.release()
        
        overall_time = time.time() - overall_start
        overall_fps = num_frames / overall_time
        
        print(f"\n📊 BENCHMARK COMPLETE")
        print(f"Overall processing: {overall_fps:.1f} FPS ({overall_time:.1f}s total)")
        
        self._analyze_results()
    
    def _analyze_results(self):
        """Analyze benchmark results and identify bottlenecks"""
        print("\n" + "=" * 60)
        print("📊 DETAILED PERFORMANCE ANALYSIS")
        print("=" * 60)
        
        # Calculate averages
        avg_times = {}
        for component, times in self.timings.items():
            if times:
                avg_times[component] = np.mean(times)
        
        # Sort by processing time to identify bottlenecks
        sorted_components = sorted(avg_times.items(), key=lambda x: x[1], reverse=True)
        
        print("\n🔍 COMPONENT BREAKDOWN (Average times):")
        print("-" * 40)
        total_avg = avg_times.get('total_processing', 0)
        
        for component, avg_time in sorted_components:
            if component != 'total_processing':
                percentage = (avg_time / total_avg * 100) if total_avg > 0 else 0
                fps_capability = 1.0 / avg_time if avg_time > 0 else float('inf')
                print(f"{component:20s}: {avg_time*1000:6.1f}ms ({percentage:5.1f}%) - {fps_capability:5.1f} FPS max")
        
        print(f"{'TOTAL':20s}: {total_avg*1000:6.1f}ms (100.0%) - {1.0/total_avg:5.1f} FPS")
        
        # Identify bottlenecks
        print(f"\n🎯 BOTTLENECK ANALYSIS:")
        print("-" * 30)
        
        bottlenecks = []
        for component, avg_time in sorted_components[:3]:  # Top 3 slowest
            if component != 'total_processing' and avg_time > 0.02:  # > 20ms
                bottlenecks.append((component, avg_time))
        
        for component, time_ms in bottlenecks:
            print(f"⚠️  {component}: {time_ms*1000:.1f}ms (needs optimization)")
        
        # Memory analysis
        if self.memory_usage:
            avg_memory = np.mean(self.memory_usage)
            max_memory = np.max(self.memory_usage)
            print(f"\n💾 MEMORY USAGE:")
            print(f"   Average: {avg_memory:.1f} MB")
            print(f"   Peak: {max_memory:.1f} MB")
        
        # Optimization recommendations
        self._generate_optimization_recommendations(sorted_components)
    
    def _generate_optimization_recommendations(self, sorted_components):
        """Generate specific optimization recommendations"""
        print(f"\n🚀 OPTIMIZATION RECOMMENDATIONS:")
        print("=" * 40)
        
        # Get the slowest components
        slowest = sorted_components[0] if sorted_components else None
        
        if slowest and slowest[0] == 'spatial_mapping':
            print("1. 🧠 SPATIAL MAPPING OPTIMIZATION:")
            print("   • Reduce depth estimation resolution (currently 1/4 scale)")
            print("   • Skip depth estimation every 2nd frame")
            print("   • Use simpler geometric projection only")
            print("   • Disable Kalman filtering for speed test")
            
        elif slowest and slowest[0] == 'edge_refinement':
            print("1. 🔍 EDGE REFINEMENT OPTIMIZATION:")
            print("   • Reduce LoG kernel size (currently 9x9)")
            print("   • Skip edge refinement for some objects")
            print("   • Use faster edge detection method")
            
        elif slowest and slowest[0] == 'yolo_detection':
            print("1. 🎯 YOLO DETECTION OPTIMIZATION:")
            print("   • Use smaller YOLO model (YOLOv8n → YOLOv8s)")
            print("   • Reduce input resolution")
            print("   • Skip detection every 2nd frame")
        
        print("\n2. 🎥 VIDEO PLAYBACK OPTIMIZATION:")
        print("   • Process at lower resolution (720p instead of 1080p)")
        print("   • Skip frames (process every 2nd frame)")
        print("   • Reduce visualization complexity")
        print("   • Use threading for parallel processing")
        
        print("\n3. ⚡ GENERAL OPTIMIZATIONS:")
        print("   • Enable GPU acceleration if available")
        print("   • Reduce memory allocations")
        print("   • Use OpenCV optimizations")
        print("   • Profile with cProfile for detailed analysis")
        
        # Calculate target optimizations
        total_avg = sorted_components[0][1] if sorted_components else 0
        target_fps = 30  # Target 30 FPS
        target_time = 1.0 / target_fps
        speedup_needed = total_avg / target_time
        
        print(f"\n📈 PERFORMANCE TARGETS:")
        print(f"   Current: {1.0/total_avg:.1f} FPS ({total_avg*1000:.1f}ms)")
        print(f"   Target: {target_fps} FPS ({target_time*1000:.1f}ms)")
        print(f"   Speedup needed: {speedup_needed:.1f}x")
        
        if speedup_needed > 2:
            print("   🔥 AGGRESSIVE OPTIMIZATION NEEDED")
        elif speedup_needed > 1.5:
            print("   ⚡ MODERATE OPTIMIZATION NEEDED")
        else:
            print("   ✅ MINOR OPTIMIZATION NEEDED")

def main():
    """Main benchmarking function"""
    benchmarker = Week3Benchmarker()
    
    # Run benchmark
    video_path = "data/test_videos/test2.mp4"
    benchmarker.benchmark_full_pipeline(video_path, num_frames=30)
    
    print(f"\n🎯 NEXT STEPS:")
    print("1. Run optimized version with recommendations")
    print("2. Test with different video resolutions")
    print("3. Profile individual components in detail")
    print("4. Implement parallel processing")

if __name__ == "__main__":
    main()
