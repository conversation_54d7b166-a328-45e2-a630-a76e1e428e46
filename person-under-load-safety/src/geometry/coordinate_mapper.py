import numpy as np
import cv2
from typing import Tuple, Dict, List, Optional
from dataclasses import dataclass
from src.geometry.edge_refinement import RefinedDetection

@dataclass
class CameraCalibration:
    """Camera calibration parameters"""
    focal_length: float
    principal_point: Tuple[float, float]
    image_size: Tuple[int, int]
    height_above_ground: float  # Camera height in meters
    tilt_angle: float          # Camera tilt in radians

@dataclass 
class Position3D:
    """3D position with uncertainty"""
    x: float  # meters
    y: float  # meters  
    z: float  # meters (height above ground)
    confidence: float
    pixel_coords: Tuple[float, float]
    
class SpatialMapper:
    """
    Maps 2D detections to 3D world coordinates
    Essential for accurate fall-zone calculations
    """
    
    def __init__(self, camera_calibration: CameraCalibration):
        """
        Initialize spatial mapper
        
        Args:
            camera_calibration: Camera parameters for 3D mapping
        """
        self.calibration = camera_calibration
        
        # Create camera matrix
        self.camera_matrix = np.array([
            [camera_calibration.focal_length, 0, camera_calibration.principal_point[0]],
            [0, camera_calibration.focal_length, camera_calibration.principal_point[1]],
            [0, 0, 1]
        ])
        
        # Ground plane assumption (workers typically on ground)
        self.ground_plane_z = 0.0
        
        print(f"📐 SpatialMapper initialized:")
        print(f"   📷 Focal length: {camera_calibration.focal_length}")
        print(f"   📏 Camera height: {camera_calibration.height_above_ground}m")
        print(f"   📐 Tilt angle: {np.degrees(camera_calibration.tilt_angle):.1f}°")
    
    def map_detections_to_3d(self, refined_detections: List[RefinedDetection], 
                           depth_map: Optional[np.ndarray] = None) -> List[Position3D]:
        """
        Map 2D detections to 3D world coordinates
        
        Args:
            refined_detections: LoG-refined detections
            depth_map: Optional depth map from DepthAnything-AC (Week 3)
            
        Returns:
            List of 3D positions
        """
        positions_3d = []
        
        for detection in refined_detections:
            # Use refined center if available, otherwise use bbox center
            if detection.center_refined:
                pixel_x, pixel_y = detection.center_refined
            else:
                bbox = detection.refined_bbox
                pixel_x = (bbox[0] + bbox[2]) / 2
                pixel_y = (bbox[1] + bbox[3]) / 2
            
            # For now, use ground plane assumption
            # (Will be enhanced with depth map in Week 3)
            world_pos = self._pixel_to_world_ground_plane(pixel_x, pixel_y)
            
            if world_pos:
                position_3d = Position3D(
                    x=world_pos[0],
                    y=world_pos[1], 
                    z=world_pos[2],
                    confidence=detection.geometric_confidence,
                    pixel_coords=(pixel_x, pixel_y)
                )
                positions_3d.append(position_3d)
        
        return positions_3d
    
    def _pixel_to_world_ground_plane(self, pixel_x: float, pixel_y: float) -> Optional[Tuple[float, float, float]]:
        """
        Convert pixel coordinates to world coordinates assuming ground plane
        
        This is a simplified version - will be enhanced with depth maps
        """
        try:
            # Convert pixel to normalized image coordinates
            x_norm = (pixel_x - self.calibration.principal_point[0]) / self.calibration.focal_length
            y_norm = (pixel_y - self.calibration.principal_point[1]) / self.calibration.focal_length
            
            # Account for camera tilt
            cos_tilt = np.cos(self.calibration.tilt_angle)
            sin_tilt = np.sin(self.calibration.tilt_angle)
            
            # Project ray to ground plane (z = 0)
            # Simplified projection - assumes camera looks down at construction site
            scale = self.calibration.height_above_ground / (y_norm * cos_tilt + sin_tilt)
            
            world_x = x_norm * scale
            world_y = (y_norm * sin_tilt - cos_tilt) * scale
            world_z = 0.0  # Ground plane assumption
            
            return (world_x, world_y, world_z)
            
        except (ZeroDivisionError, ValueError):
            return None
    
    def calculate_distance_3d(self, pos1: Position3D, pos2: Position3D) -> float:
        """Calculate 3D Euclidean distance between two positions"""
        dx = pos1.x - pos2.x
        dy = pos1.y - pos2.y  
        dz = pos1.z - pos2.z
        
        return np.sqrt(dx*dx + dy*dy + dz*dz)
    
    def estimate_object_dimensions(self, detection: RefinedDetection, position_3d: Position3D) -> Tuple[float, float, float]:
        """
        Estimate real-world object dimensions from pixel size and distance
        
        Returns:
            (width, height, depth) in meters
        """
        bbox = detection.refined_bbox
        pixel_width = bbox[2] - bbox[0]
        pixel_height = bbox[3] - bbox[1]
        
        # Distance from camera
        distance = np.sqrt(position_3d.x**2 + position_3d.y**2 + position_3d.z**2)
        
        # Convert pixel size to world size using pinhole camera model
        meters_per_pixel = distance / self.calibration.focal_length
        
        world_width = pixel_width * meters_per_pixel
        world_height = pixel_height * meters_per_pixel
        
        # Estimate depth based on object type (simplified)
        world_depth = min(world_width, world_height) * 0.5  # Rough approximation
        
        return (world_width, world_height, world_depth)
