[project]
name = "person-under-load-safety"
version = "0.1.0"
description = "AI-powered spatial awareness system to prevent Person-Under-Load incidents"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "ultralytics>=8.0.0",
    "opencv-python>=4.8.0",
    "numpy>=1.24.0",
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "supervision>=0.16.0",
    "scipy>=1.10.0",
    "transformers>=4.30.0",
    "timm>=0.9.0",
    "pyyaml>=6.0",
    "tqdm>=4.65.0",
    "matplotlib>=3.7.0",
    "pillow>=10.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.ruff]
target-version = "py311"
line-length = 88
