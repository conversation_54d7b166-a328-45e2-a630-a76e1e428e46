#!/usr/bin/env python3
"""
Simple Week 3 Test - Debug Video Display
Tests Week 3 enhancements with console output and saves frames
"""

import cv2
import numpy as np
import time
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.main import PersonUnderLoadSystem

def test_week3_simple():
    """Simple test with console output and frame saving"""
    print("🧪 Week 3 Simple Test - Debug Mode")
    print("=" * 50)
    
    # Initialize enhanced system
    print("🚀 Initializing Week 3 Enhanced System...")
    system = PersonUnderLoadSystem(use_enhanced_mapping=True)
    
    # Test video path
    video_path = "data/test_videos/test2.mp4"
    print(f"📹 Testing with: {video_path}")
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ Error: Could not open video {video_path}")
        return
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"📊 Video properties:")
    print(f"   Resolution: {width}x{height}")
    print(f"   FPS: {fps}")
    print(f"   Total frames: {frame_count}")
    print()
    
    # Process first 10 frames
    frames_to_process = min(10, frame_count)
    print(f"🎬 Processing first {frames_to_process} frames...")
    print("=" * 50)
    
    processing_times = []
    
    for frame_idx in range(frames_to_process):
        # Read frame
        ret, frame = cap.read()
        if not ret:
            print(f"❌ Failed to read frame {frame_idx}")
            break
        
        print(f"\n🎬 Frame {frame_idx + 1}/{frames_to_process}")
        print("-" * 30)
        
        # Process frame
        start_time = time.time()
        try:
            annotated_frame, stats = system.process_frame(frame)
            processing_time = time.time() - start_time
            processing_times.append(processing_time)
            
            # Print stats
            print(f"⚡ Processing time: {processing_time*1000:.1f}ms")
            print(f"🎯 FPS capability: {1.0/processing_time:.1f}")
            
            # Detection stats
            detection_stats = stats.get('detection_stats', {})
            print(f"👥 Objects detected: {detection_stats.get('total_detections', 0)}")
            
            # Safety status
            safety_status = stats.get('safety_status', {})
            workers = safety_status.get('workers_detected', 0)
            loads = safety_status.get('loads_detected', 0)
            warnings = len(safety_status.get('warnings', []))
            overall_status = safety_status.get('overall_status', 'UNKNOWN')
            
            print(f"👷 Workers: {workers}")
            print(f"🏗️ Loads: {loads}")
            print(f"⚠️ Warnings: {warnings}")
            print(f"🛡️ Status: {overall_status}")
            
            # Week 3 enhancements
            week3_stats = stats.get('week3_enhancements', {})
            if week3_stats.get('enhanced_mapping_enabled'):
                print("✅ DepthAnything-AC: Active")
                print("✅ Kalman Smoothing: Active")
                
                spatial_stats = stats.get('spatial_stats', {})
                if 'avg_processing_time' in spatial_stats:
                    print(f"🧠 Depth estimation: {spatial_stats['avg_processing_time']*1000:.1f}ms")
                if 'avg_accuracy_score' in spatial_stats:
                    print(f"🎯 Accuracy score: {spatial_stats['avg_accuracy_score']:.3f}")
            
            # Save annotated frame
            output_filename = f"week3_test_frame_{frame_idx:03d}.jpg"
            cv2.imwrite(output_filename, annotated_frame)
            print(f"💾 Saved: {output_filename}")
            
        except Exception as e:
            print(f"❌ Error processing frame {frame_idx}: {str(e)}")
            import traceback
            traceback.print_exc()
            break
    
    cap.release()
    
    # Final summary
    print("\n" + "=" * 50)
    print("📊 WEEK 3 SIMPLE TEST SUMMARY")
    print("=" * 50)
    
    if processing_times:
        avg_processing = np.mean(processing_times)
        min_processing = np.min(processing_times)
        max_processing = np.max(processing_times)
        avg_fps = 1.0 / avg_processing
        
        print(f"⚡ Processing Performance:")
        print(f"   Average: {avg_processing*1000:.1f}ms ({avg_fps:.1f} FPS)")
        print(f"   Min: {min_processing*1000:.1f}ms ({1.0/min_processing:.1f} FPS)")
        print(f"   Max: {max_processing*1000:.1f}ms ({1.0/max_processing:.1f} FPS)")
        
        # Week 3 targets
        print(f"\n🎯 Week 3 Targets:")
        print(f"   Target FPS: 15+")
        print(f"   Achieved: {avg_fps:.1f} FPS {'✅' if avg_fps >= 15 else '⚠️'}")
        
        if avg_fps >= 15:
            print("🏆 EXCELLENT: Week 3 performance target achieved!")
        elif avg_fps >= 10:
            print("✅ GOOD: Strong performance, close to target")
        else:
            print("⚠️ NEEDS OPTIMIZATION: Below performance target")
    
    print(f"\n🛡️ Week 3 Enhanced Features Tested:")
    print(f"   ✅ DepthAnything-AC Integration")
    print(f"   ✅ Kalman Filtering")
    print(f"   ✅ Enhanced 3D Spatial Mapping")
    print(f"   ✅ Performance Optimization")
    
    print(f"\n💾 Output frames saved for inspection")
    print(f"📁 Check current directory for week3_test_frame_*.jpg files")

if __name__ == "__main__":
    test_week3_simple()
