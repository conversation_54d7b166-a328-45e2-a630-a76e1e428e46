import cv2
import numpy as np
from src.detection.yolo_detector import ConstructionSiteDetector
from src.detection.object_tracker import SafetyTracker
from src.utils.visualization import SafetyVisualizer
from src.geometry.edge_refinement import LaplacianEdgeRefiner
from src.geometry.coordinate_mapper import SpatialMapper, CameraCalibration, Position3D
from src.geometry.enhanced_spatial_mapper import EnhancedSpatialMapper
import time
from typing import Tuple, Dict

class PersonUnderLoadSystem:
    """Week 3 Enhanced system with DepthAnything-AC integration"""

    def __init__(self, use_enhanced_mapping: bool = True, performance_mode: bool = False, speed_mode: bool = False):
        self.detector = ConstructionSiteDetector()
        self.tracker = SafetyTracker()
        self.performance_mode = performance_mode
        self.speed_mode = speed_mode

        # LoG edge refinement - OPTIMIZED FOR SPEED
        if speed_mode:
            # Fast mode: smaller kernel, higher threshold
            self.edge_refiner = LaplacianEdgeRefiner(sigma=0.8, threshold=0.2)
            self.edge_refinement_enabled = False  # Skip edge refinement in speed mode
        else:
            self.edge_refiner = LaplacianEdgeRefiner(sigma=1.2, threshold=0.1)
            self.edge_refinement_enabled = True

        # Camera calibration
        camera_cal = CameraCalibration(
            focal_length=800,  # pixels - estimate for typical camera
            principal_point=(960, 540),  # image center for 1920x1080
            image_size=(1920, 1080),
            height_above_ground=5.0,  # 5 meters above ground
            tilt_angle=np.radians(15)  # 15 degree downward tilt
        )

        # Week 3: Enhanced spatial mapping with depth integration
        if use_enhanced_mapping and not speed_mode:
            self.spatial_mapper = EnhancedSpatialMapper(camera_cal)
            self.use_enhanced = True
        else:
            # Use faster standard mapping in speed mode
            self.spatial_mapper = SpatialMapper(camera_cal)
            self.use_enhanced = False

        self.visualizer = SafetyVisualizer()

        # Performance optimization
        self.frame_skip_counter = 0
        if speed_mode:
            self.frame_skip_interval = 1  # Process every frame but with optimizations
        elif performance_mode:
            self.frame_skip_interval = 2  # Process every 2nd frame
        else:
            self.frame_skip_interval = 1  # Process every frame

        print("🚀 Week 3 Enhanced Person-Under-Load Safety System Initialized")
        if speed_mode:
            print("⚡ SPEED MODE ENABLED - Optimized for maximum FPS")
        print("📋 Components loaded:")
        print("   ✅ YOLO Detector")
        print("   ✅ Multi-Object Tracker")
        if self.edge_refinement_enabled:
            print("   ✅ LoG Edge Refinement")
        else:
            print("   ⚡ LoG Edge Refinement (DISABLED for speed)")
        if self.use_enhanced:
            print("   ✅ Enhanced 3D Spatial Mapping (DepthAnything-AC)")
            print("   ✅ Kalman Filtering for Temporal Smoothing")
        else:
            print("   ⚡ Standard 3D Spatial Mapping (FAST MODE)")
        print("   ✅ Enhanced Visualization")
    
    def process_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """Week 3 Enhanced processing with depth integration and temporal smoothing"""

        # Stage 1: YOLO Detection
        detections = self.detector.detect_objects(frame)
        objects_info = self.detector.get_object_details(detections)

        # Stage 2: Object Tracking
        tracked_objects = self.tracker.update(detections, objects_info)

        # Stage 3: LoG Edge Refinement (OPTIMIZED)
        if self.edge_refinement_enabled:
            refined_detections = self.edge_refiner.refine_detections(
                frame, detections, objects_info
            )
        else:
            # Skip edge refinement for speed - use original detections
            from src.geometry.edge_refinement import RefinedDetection
            refined_detections = []
            for i in range(len(detections)):
                bbox = detections.xyxy[i]  # Get bbox from sv.Detections format
                refined_detections.append(RefinedDetection(
                    original_bbox=[float(bbox[0]), float(bbox[1]), float(bbox[2] - bbox[0]), float(bbox[3] - bbox[1])],
                    refined_bbox=[float(bbox[0]), float(bbox[1]), float(bbox[2] - bbox[0]), float(bbox[3] - bbox[1])],
                    edge_strength=100.0,  # Default value
                    geometric_confidence=0.8,  # Default value
                    contour_points=None,
                    center_refined=None
                ))

        # Stage 4: Enhanced 3D Spatial Mapping with Depth Integration (OPTIMIZED)
        if self.use_enhanced and not self.speed_mode:
            positions_3d = self.spatial_mapper.map_detections_to_3d_enhanced(refined_detections, frame)
        else:
            positions_3d = self.spatial_mapper.map_detections_to_3d(refined_detections)

        # Stage 5: Enhanced Safety Assessment
        safety_status = self._enhanced_safety_check(tracked_objects, positions_3d)

        # Stage 6: Enhanced Visualization
        annotated_frame = self.visualizer.draw_enhanced_detections(
            frame, tracked_objects, refined_detections, positions_3d, safety_status
        )

        # Compile Week 3 enhanced stats
        spatial_stats = self.spatial_mapper.get_performance_stats() if self.use_enhanced else {
            '3d_positions': len(positions_3d),
            'avg_distance_from_camera': np.mean([np.sqrt(p.x**2 + p.y**2 + p.z**2) for p in positions_3d]) if positions_3d else 0,
        }

        stats = {
            'detection_stats': self.detector.get_performance_stats(),
            'tracking_stats': self.tracker.get_tracking_stats(),
            'refinement_stats': {
                'refined_detections': len(refined_detections),
                'avg_edge_strength': np.mean([d.edge_strength for d in refined_detections]) if refined_detections else 0,
                'avg_geometric_confidence': np.mean([d.geometric_confidence for d in refined_detections]) if refined_detections else 0,
            },
            'spatial_stats': spatial_stats,
            'safety_status': safety_status,
            'week3_enhancements': {
                'enhanced_mapping_enabled': self.use_enhanced,
                'depth_integration': self.use_enhanced,
                'temporal_smoothing': self.use_enhanced,
            }
        }

        return annotated_frame, stats
    
    def _enhanced_safety_check(self, tracked_objects, positions_3d) -> Dict:
        """Enhanced safety assessment with 3D spatial intelligence"""

        workers = [obj for obj in tracked_objects if obj.class_name == 'person']
        loads = [obj for obj in tracked_objects if 'load' in obj.class_name or 'crane' in obj.class_name or 'equipment' in obj.class_name]

        # Enhanced 3D distance-based analysis
        warnings = []

        for i, worker in enumerate(workers):
            worker_pos_3d = positions_3d[i] if i < len(positions_3d) else None

            if not worker_pos_3d:
                continue

            for j, load in enumerate(loads):
                # Find corresponding 3D position for load
                load_pos_3d = None
                for k, pos in enumerate(positions_3d):
                    if k >= len(workers):  # Load positions come after worker positions
                        load_pos_3d = pos
                        break

                if not load_pos_3d:
                    continue

                # Calculate TRUE 3D distance
                distance_3d = self.spatial_mapper.calculate_distance_3d(worker_pos_3d, load_pos_3d)

                # Enhanced risk assessment based on 3D coordinates
                # Consider vertical separation (Z-axis is critical for overhead loads)
                vertical_separation = abs(load_pos_3d.z - worker_pos_3d.z)
                horizontal_distance = np.sqrt((load_pos_3d.x - worker_pos_3d.x)**2 +
                                            (load_pos_3d.y - worker_pos_3d.y)**2)

                # Risk calculation considering 3D geometry
                if vertical_separation > 2.0:  # Load is overhead
                    danger_radius = 3.0  # 3 meter safety radius for overhead loads
                    if horizontal_distance < danger_radius:
                        risk_level = 'HIGH' if horizontal_distance < 1.5 else 'MEDIUM'
                        warnings.append({
                            'worker_id': worker.id,
                            'load_id': load.id,
                            'distance_3d': distance_3d,
                            'horizontal_distance': horizontal_distance,
                            'vertical_separation': vertical_separation,
                            'risk_level': risk_level,
                            'warning_type': 'OVERHEAD_LOAD'
                        })

        return {
            'workers_detected': len(workers),
            'loads_detected': len(loads),
            'warnings': warnings,
            'overall_status': 'DANGER' if any(w['risk_level'] == 'HIGH' for w in warnings) else 'SAFE',
            '3d_analysis_active': True,
        }

def main():
    """Test the system with webcam or video file"""
    
    system = PersonUnderLoadSystem()
    
    # Use webcam (0) or provide video file path
    cap = cv2.VideoCapture(0)  # Change to video file path for testing
    
    print("\n🎥 Starting video processing...")
    print("Press 'q' to quit, 's' to save frame")
    
    frame_count = 0
    fps_counter = time.time()
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Process frame
        annotated_frame, stats = system.process_frame(frame)
        
        # Display FPS
        frame_count += 1
        if frame_count % 30 == 0:  # Update every 30 frames
            fps = 30 / (time.time() - fps_counter)
            fps_counter = time.time()
            print(f"🚀 FPS: {fps:.1f} | Workers: {stats['safety_status']['workers_detected']} | Status: {stats['safety_status']['overall_status']}")
        
        # Show frame
        cv2.imshow('Person-Under-Load Safety System', annotated_frame)
        
        # Handle keys
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            cv2.imwrite(f'safety_frame_{frame_count}.jpg', annotated_frame)
            print(f"💾 Frame saved: safety_frame_{frame_count}.jpg")
    
    cap.release()
    cv2.destroyAllWindows()
    
    # Final stats
    print("\n📊 Final Statistics:")
    final_stats = system.detector.get_performance_stats()
    for key, value in final_stats.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    main()
