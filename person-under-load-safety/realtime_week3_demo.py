#!/usr/bin/env python3
"""
Week 3 Enhanced Demo: DepthAnything-AC Integration
Real-time demonstration of enhanced spatial mapping with depth integration

Features:
- DepthAnything-AC depth estimation
- <PERSON><PERSON> filtering for temporal smoothing
- Improved distance calibration
- Performance optimization
"""

import cv2
import numpy as np
import time
import argparse
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.main import PersonUnderLoadSystem

class Week3Demo:
    """Week 3 enhanced demo with performance monitoring"""
    
    def __init__(self, video_source=0, use_enhanced=True):
        """Initialize Week 3 demo"""
        self.video_source = video_source
        self.use_enhanced = use_enhanced
        
        # Initialize enhanced system
        print("🚀 Initializing Week 3 Enhanced System...")
        self.system = PersonUnderLoadSystem(use_enhanced_mapping=use_enhanced)
        
        # Performance tracking
        self.frame_times = []
        self.processing_times = []
        self.fps_history = []
        
        # Display settings
        self.show_depth_map = True
        self.show_performance_overlay = True
        
        print(f"📹 Video source: {video_source}")
        print(f"🧠 Enhanced mapping: {'Enabled' if use_enhanced else 'Disabled'}")
        print("🎮 Controls:")
        print("   'q' - Quit")
        print("   's' - Save frame")
        print("   'd' - Toggle depth map display")
        print("   'p' - Toggle performance overlay")
        print("   'r' - Reset performance stats")
    
    def run(self):
        """Run the Week 3 demo"""
        # Initialize video capture
        cap = cv2.VideoCapture(self.video_source)
        
        if not cap.isOpened():
            print(f"❌ Error: Could not open video source {self.video_source}")
            return
        
        # Set video properties for better performance
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        print("\n🎥 Starting Week 3 Enhanced Demo...")
        print("=" * 60)
        
        frame_count = 0
        start_time = time.time()
        last_fps_update = time.time()
        
        try:
            while True:
                frame_start = time.time()

                # Capture frame
                ret, frame = cap.read()
                if not ret:
                    if isinstance(self.video_source, str):
                        # Video file ended, restart from beginning
                        print("🔄 Video ended, restarting loop...")
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        ret, frame = cap.read()
                        if not ret:
                            print("❌ Failed to restart video")
                            break
                    else:
                        print("❌ Failed to capture frame")
                        break
                
                # Process frame with enhanced system
                processing_start = time.time()
                annotated_frame, stats = self.system.process_frame(frame)
                processing_time = time.time() - processing_start
                
                # Update performance tracking
                frame_time = time.time() - frame_start
                self.frame_times.append(frame_time)
                self.processing_times.append(processing_time)
                
                # Calculate FPS
                if time.time() - last_fps_update >= 1.0:
                    current_fps = len(self.frame_times) / sum(self.frame_times) if self.frame_times else 0
                    self.fps_history.append(current_fps)
                    last_fps_update = time.time()
                    
                    # Keep only recent history
                    if len(self.fps_history) > 10:
                        self.fps_history.pop(0)
                    if len(self.frame_times) > 30:
                        self.frame_times = self.frame_times[-30:]
                        self.processing_times = self.processing_times[-30:]
                
                # Add performance overlay
                if self.show_performance_overlay:
                    annotated_frame = self._add_performance_overlay(annotated_frame, stats, processing_time)
                
                # Display frame
                cv2.imshow('Week 3 Enhanced Person-Under-Load Safety System', annotated_frame)
                
                # Handle keyboard input
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    self._save_frame(annotated_frame, frame_count, stats)
                elif key == ord('d'):
                    self.show_depth_map = not self.show_depth_map
                    print(f"🔍 Depth map display: {'ON' if self.show_depth_map else 'OFF'}")
                elif key == ord('p'):
                    self.show_performance_overlay = not self.show_performance_overlay
                    print(f"📊 Performance overlay: {'ON' if self.show_performance_overlay else 'OFF'}")
                elif key == ord('r'):
                    self._reset_performance_stats()
                
                frame_count += 1
                
                # Print periodic stats
                if frame_count % 100 == 0:
                    self._print_performance_summary(frame_count, time.time() - start_time)
        
        except KeyboardInterrupt:
            print("\n⏹️ Demo interrupted by user")
        
        finally:
            # Cleanup
            cap.release()
            cv2.destroyAllWindows()
            
            # Final performance report
            self._print_final_report(frame_count, time.time() - start_time)
    
    def _add_performance_overlay(self, frame, stats, processing_time):
        """Add performance information overlay"""
        overlay = frame.copy()
        
        # Performance metrics
        current_fps = 1.0 / processing_time if processing_time > 0 else 0
        avg_fps = np.mean(self.fps_history) if self.fps_history else 0
        
        # Safety status
        safety_status = stats.get('safety_status', {})
        workers = safety_status.get('workers_detected', 0)
        loads = safety_status.get('loads_detected', 0)
        warnings = len(safety_status.get('warnings', []))
        overall_status = safety_status.get('overall_status', 'UNKNOWN')
        
        # Week 3 enhancements
        week3_stats = stats.get('week3_enhancements', {})
        enhanced_mapping = week3_stats.get('enhanced_mapping_enabled', False)
        
        # Spatial stats
        spatial_stats = stats.get('spatial_stats', {})
        
        # Create overlay text
        y_offset = 30
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 2
        
        # Header
        cv2.putText(overlay, "WEEK 3 ENHANCED SYSTEM", (10, y_offset), 
                   font, 0.8, (0, 255, 255), thickness)
        y_offset += 40
        
        # Performance metrics
        cv2.putText(overlay, f"FPS: {current_fps:.1f} (avg: {avg_fps:.1f})", 
                   (10, y_offset), font, font_scale, (0, 255, 0), thickness)
        y_offset += 25
        
        cv2.putText(overlay, f"Processing: {processing_time*1000:.1f}ms", 
                   (10, y_offset), font, font_scale, (0, 255, 0), thickness)
        y_offset += 25
        
        # Detection stats
        cv2.putText(overlay, f"Workers: {workers} | Loads: {loads}", 
                   (10, y_offset), font, font_scale, (255, 255, 255), thickness)
        y_offset += 25
        
        # Safety status
        status_color = (0, 0, 255) if overall_status == 'DANGER' else (0, 255, 0)
        cv2.putText(overlay, f"Status: {overall_status}", 
                   (10, y_offset), font, font_scale, status_color, thickness)
        y_offset += 25
        
        if warnings > 0:
            cv2.putText(overlay, f"⚠️ Warnings: {warnings}", 
                       (10, y_offset), font, font_scale, (0, 165, 255), thickness)
            y_offset += 25
        
        # Week 3 enhancements
        if enhanced_mapping:
            cv2.putText(overlay, "✅ DepthAnything-AC Active", 
                       (10, y_offset), font, font_scale, (0, 255, 255), thickness)
            y_offset += 25
            
            cv2.putText(overlay, "✅ Kalman Smoothing Active", 
                       (10, y_offset), font, font_scale, (0, 255, 255), thickness)
            y_offset += 25
            
            # Enhanced spatial stats
            if 'avg_processing_time' in spatial_stats:
                cv2.putText(overlay, f"Depth Est: {spatial_stats['avg_processing_time']*1000:.1f}ms", 
                           (10, y_offset), font, font_scale, (255, 255, 0), thickness)
                y_offset += 25
            
            if 'avg_accuracy_score' in spatial_stats:
                cv2.putText(overlay, f"Accuracy: {spatial_stats['avg_accuracy_score']:.3f}", 
                           (10, y_offset), font, font_scale, (255, 255, 0), thickness)
                y_offset += 25
        
        # Blend overlay
        alpha = 0.7
        cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0, frame)
        
        return frame
    
    def _save_frame(self, frame, frame_count, stats):
        """Save current frame with metadata"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"week3_frame_{timestamp}_{frame_count:06d}.jpg"
        
        cv2.imwrite(filename, frame)
        
        # Save stats
        stats_filename = f"week3_stats_{timestamp}_{frame_count:06d}.txt"
        with open(stats_filename, 'w') as f:
            f.write("Week 3 Enhanced System Stats\n")
            f.write("=" * 40 + "\n")
            for key, value in stats.items():
                f.write(f"{key}: {value}\n")
        
        print(f"💾 Saved: {filename} and {stats_filename}")
    
    def _reset_performance_stats(self):
        """Reset performance statistics"""
        self.frame_times.clear()
        self.processing_times.clear()
        self.fps_history.clear()
        print("🔄 Performance stats reset")
    
    def _print_performance_summary(self, frame_count, elapsed_time):
        """Print performance summary"""
        avg_fps = frame_count / elapsed_time if elapsed_time > 0 else 0
        avg_processing = np.mean(self.processing_times) if self.processing_times else 0
        
        print(f"\n📊 Frame {frame_count} | "
              f"Avg FPS: {avg_fps:.1f} | "
              f"Avg Processing: {avg_processing*1000:.1f}ms")
    
    def _print_final_report(self, total_frames, total_time):
        """Print final performance report"""
        print("\n" + "=" * 60)
        print("📊 WEEK 3 ENHANCED DEMO - FINAL REPORT")
        print("=" * 60)
        
        if total_time > 0:
            avg_fps = total_frames / total_time
            print(f"🎥 Total frames processed: {total_frames}")
            print(f"⏱️ Total time: {total_time:.1f}s")
            print(f"📈 Average FPS: {avg_fps:.1f}")
        
        if self.processing_times:
            avg_processing = np.mean(self.processing_times)
            min_processing = np.min(self.processing_times)
            max_processing = np.max(self.processing_times)
            
            print(f"⚡ Processing time - Avg: {avg_processing*1000:.1f}ms | "
                  f"Min: {min_processing*1000:.1f}ms | "
                  f"Max: {max_processing*1000:.1f}ms")
        
        print("\n🛡️ Week 3 enhancements successfully demonstrated!")
        print("   ✅ DepthAnything-AC integration")
        print("   ✅ Kalman filtering for temporal smoothing")
        print("   ✅ Enhanced distance calibration")
        print("   ✅ Performance optimization")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Week 3 Enhanced Person-Under-Load Safety Demo')
    parser.add_argument('--video', type=str, default='0', 
                       help='Video source (0 for webcam, path for video file)')
    parser.add_argument('--no-enhanced', action='store_true',
                       help='Disable enhanced mapping (use standard mapping)')
    
    args = parser.parse_args()
    
    # Parse video source
    video_source = args.video
    if video_source.isdigit():
        video_source = int(video_source)
    
    # Create and run demo
    demo = Week3Demo(video_source=video_source, use_enhanced=not args.no_enhanced)
    demo.run()

if __name__ == "__main__":
    main()
