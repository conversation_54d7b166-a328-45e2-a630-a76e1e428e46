#!/usr/bin/env python3
"""
Brief test of the main system without GUI
"""

import cv2
import numpy as np
from src.main import PersonUnderLoadSystem

def test_main_system():
    """Test the main system with a synthetic frame"""
    
    print("🧪 Testing Main System (No GUI)...")
    
    # Initialize system
    system = PersonUnderLoadSystem()
    
    # Create a test frame with some objects
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Add some synthetic "objects" (white rectangles that might be detected as people)
    cv2.rectangle(frame, (100, 100), (150, 250), (255, 255, 255), -1)  # Person-like shape
    cv2.rectangle(frame, (300, 80), (350, 200), (200, 200, 200), -1)   # Another object
    cv2.rectangle(frame, (500, 50), (550, 150), (150, 150, 150), -1)   # Third object
    
    print("🎬 Processing test frame...")
    
    # Process the frame
    annotated_frame, stats = system.process_frame(frame)
    
    print("✅ Frame processed successfully!")
    print(f"📊 Results:")
    print(f"   - Workers detected: {stats['safety_status']['workers_detected']}")
    print(f"   - Loads detected: {stats['safety_status']['loads_detected']}")
    print(f"   - Overall status: {stats['safety_status']['overall_status']}")
    print(f"   - Warnings: {len(stats['safety_status']['warnings'])}")
    
    # Test a few more frames to check tracking
    print("\n🔄 Testing tracking with multiple frames...")
    
    for i in range(3):
        # Slightly move the objects to test tracking
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        offset = i * 10
        cv2.rectangle(frame, (100 + offset, 100), (150 + offset, 250), (255, 255, 255), -1)
        cv2.rectangle(frame, (300 + offset, 80), (350 + offset, 200), (200, 200, 200), -1)
        
        annotated_frame, stats = system.process_frame(frame)
        print(f"   Frame {i+2}: {stats['tracking_stats']['total_tracked']} objects tracked")
    
    print("\n✅ Main system test completed successfully!")
    return True

if __name__ == "__main__":
    try:
        test_main_system()
        print("\n🎉 All main system tests passed!")
    except Exception as e:
        print(f"\n❌ Main system test failed: {e}")
        import traceback
        traceback.print_exc()
