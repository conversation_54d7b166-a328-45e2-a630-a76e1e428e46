#!/usr/bin/env python3
"""
Real-time video demo showing live detection on test2 video
"""

import cv2
import numpy as np
import time
from src.main import PersonUnderLoadSystem

def realtime_video_demo():
    """Play test2 video with real-time detection overlay"""
    
    video_path = "data/test_videos/test2.mp4"
    
    print("🎬 REAL-TIME DEMO: Person-Under-Load Detection")
    print("=" * 60)
    print("🎮 Controls:")
    print("   SPACE - Pause/Resume")
    print("   'q' - Quit")
    print("   's' - Save current frame")
    print("   'r' - Reset to beginning")
    print("   '+' - Speed up")
    print("   '-' - Slow down")
    print("=" * 60)
    
    # Initialize system
    print("🚀 Initializing detection system...")
    system = PersonUnderLoadSystem()
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"❌ Could not open video: {video_path}")
        return
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📊 Video: {width}x{height} @ {fps:.1f} FPS, {total_frames} frames ({total_frames/fps:.1f}s)")
    print(f"🚀 Starting real-time playback...")
    
    # Playback control variables
    paused = False
    speed_multiplier = 1.0
    frame_delay = 1.0 / fps  # Original frame delay
    saved_frame_count = 0
    
    # Performance tracking
    processing_times = []
    display_fps = 0
    fps_update_time = time.time()
    fps_frame_count = 0
    
    while True:
        if not paused:
            ret, frame = cap.read()
            if not ret:
                print("📹 Video ended. Press 'r' to restart or 'q' to quit.")
                paused = True
                continue
        
        if not paused:
            # Process frame
            start_time = time.time()
            annotated_frame, stats = system.process_frame(frame)
            processing_time = time.time() - start_time
            processing_times.append(processing_time)
            
            # Get current frame position
            current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
            progress = (current_frame / total_frames) * 100
            
            # Extract stats
            safety_status = stats['safety_status']
            workers = safety_status['workers_detected']
            loads = safety_status['loads_detected']
            warnings = len(safety_status['warnings'])
            overall_status = safety_status['overall_status']
            
            # Add performance info overlay
            perf_text = f"Frame: {current_frame}/{total_frames} ({progress:.1f}%) | Processing: {processing_time*1000:.1f}ms | Display FPS: {display_fps:.1f}"
            cv2.putText(annotated_frame, perf_text, (10, height - 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(annotated_frame, perf_text, (10, height - 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
            
            # Add control info
            control_text = f"Speed: {speed_multiplier:.1f}x | SPACE=Pause, Q=Quit, S=Save, R=Reset, +/-=Speed"
            cv2.putText(annotated_frame, control_text, (10, height - 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
            cv2.putText(annotated_frame, control_text, (10, height - 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            
            # Calculate display FPS
            fps_frame_count += 1
            if time.time() - fps_update_time >= 1.0:  # Update every second
                display_fps = fps_frame_count / (time.time() - fps_update_time)
                fps_update_time = time.time()
                fps_frame_count = 0
        
        else:
            # Show paused frame or black screen
            if 'annotated_frame' in locals():
                # Add pause overlay
                pause_overlay = annotated_frame.copy()
                cv2.rectangle(pause_overlay, (width//2 - 100, height//2 - 50), 
                             (width//2 + 100, height//2 + 50), (0, 0, 0), -1)
                cv2.putText(pause_overlay, "PAUSED", (width//2 - 80, height//2 + 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 3)
                annotated_frame = pause_overlay
            else:
                annotated_frame = np.zeros((height, width, 3), dtype=np.uint8)
                cv2.putText(annotated_frame, "Video Ended - Press 'r' to restart", 
                           (width//2 - 200, height//2), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Display frame
        cv2.imshow('Person-Under-Load Safety System - Real-Time Demo', annotated_frame)
        
        # Handle keyboard input
        key = cv2.waitKey(int(frame_delay * 1000 / speed_multiplier)) & 0xFF
        
        if key == ord('q'):
            print("👋 Quitting demo...")
            break
        elif key == ord(' '):  # Space bar
            paused = not paused
            if paused:
                print("⏸️  Paused")
            else:
                print("▶️  Resumed")
        elif key == ord('s'):
            if 'annotated_frame' in locals():
                saved_frame_count += 1
                filename = f"realtime_capture_{saved_frame_count:03d}.jpg"
                cv2.imwrite(filename, annotated_frame)
                print(f"💾 Saved frame: {filename}")
        elif key == ord('r'):
            print("🔄 Restarting video...")
            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            paused = False
        elif key == ord('+') or key == ord('='):
            speed_multiplier = min(speed_multiplier * 1.5, 5.0)
            print(f"⚡ Speed: {speed_multiplier:.1f}x")
        elif key == ord('-') or key == ord('_'):
            speed_multiplier = max(speed_multiplier / 1.5, 0.25)
            print(f"🐌 Speed: {speed_multiplier:.1f}x")
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    
    # Final statistics
    if processing_times:
        avg_processing = np.mean(processing_times)
        max_processing = np.max(processing_times)
        min_processing = np.min(processing_times)
        
        print(f"\n📊 PERFORMANCE STATISTICS:")
        print(f"   - Frames processed: {len(processing_times)}")
        print(f"   - Average processing time: {avg_processing*1000:.1f}ms")
        print(f"   - Min processing time: {min_processing*1000:.1f}ms")
        print(f"   - Max processing time: {max_processing*1000:.1f}ms")
        print(f"   - Theoretical max FPS: {1/avg_processing:.1f}")
        print(f"   - Frames saved: {saved_frame_count}")
    
    print(f"✅ Real-time demo completed!")

if __name__ == "__main__":
    realtime_video_demo()
