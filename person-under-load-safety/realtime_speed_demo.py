#!/usr/bin/env python3
"""
Speed-Optimized Demo: Maximum FPS Performance
Optimized for full-speed video playback with minimal processing overhead

Features:
- Disabled edge refinement (saves 118ms per frame)
- Standard spatial mapping (saves 31ms per frame)
- Reduced resolution processing
- Frame skipping options
- Performance monitoring
"""

import cv2
import numpy as np
import time
import argparse
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.main import PersonUnderLoadSystem

class SpeedOptimizedDemo:
    """Speed-optimized demo for maximum FPS"""
    
    def __init__(self, video_source=0, resolution_scale=1.0, frame_skip=1):
        """Initialize speed-optimized demo"""
        self.video_source = video_source
        self.resolution_scale = resolution_scale
        self.frame_skip = frame_skip
        
        # Initialize speed-optimized system
        print("⚡ Initializing SPEED-OPTIMIZED System...")
        self.system = PersonUnderLoadSystem(
            use_enhanced_mapping=False,  # Disable enhanced mapping for speed
            performance_mode=False,
            speed_mode=True  # Enable speed mode
        )
        
        # Performance tracking
        self.frame_times = []
        self.processing_times = []
        self.fps_history = []
        
        # Display settings
        self.show_performance_overlay = True
        
        print(f"📹 Video source: {video_source}")
        print(f"📏 Resolution scale: {resolution_scale}")
        print(f"⏭️ Frame skip: {frame_skip}")
        print("🎮 Controls:")
        print("   'q' - Quit")
        print("   's' - Save frame")
        print("   'p' - Toggle performance overlay")
        print("   'r' - Reset performance stats")
        print("   '+' - Increase frame skip")
        print("   '-' - Decrease frame skip")
    
    def run(self):
        """Run the speed-optimized demo"""
        # Initialize video capture
        cap = cv2.VideoCapture(self.video_source)
        
        if not cap.isOpened():
            print(f"❌ Error: Could not open video source {self.video_source}")
            return
        
        # Get original video properties
        original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        # Calculate target resolution
        target_width = int(original_width * self.resolution_scale)
        target_height = int(original_height * self.resolution_scale)
        
        print(f"\n📊 Video Properties:")
        print(f"   Original: {original_width}x{original_height} @ {fps} FPS")
        print(f"   Processing: {target_width}x{target_height}")
        print(f"   Frame skip: {self.frame_skip}")
        
        print("\n⚡ Starting Speed-Optimized Demo...")
        print("=" * 60)
        
        frame_count = 0
        processed_count = 0
        start_time = time.time()
        last_fps_update = time.time()
        
        try:
            while True:
                frame_start = time.time()
                
                # Capture frame
                ret, frame = cap.read()
                if not ret:
                    if isinstance(self.video_source, str):
                        # Video file ended, restart
                        print("🔄 Video ended, restarting loop...")
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        continue
                    else:
                        print("❌ Failed to capture frame")
                        break
                
                frame_count += 1
                
                # Frame skipping for additional speed
                if frame_count % self.frame_skip != 0:
                    continue
                
                processed_count += 1
                
                # Resize frame for faster processing
                if self.resolution_scale != 1.0:
                    frame = cv2.resize(frame, (target_width, target_height))
                
                # Process frame with speed-optimized system
                processing_start = time.time()
                annotated_frame, stats = self.system.process_frame(frame)
                processing_time = time.time() - processing_start
                
                # Resize back to original size for display
                if self.resolution_scale != 1.0:
                    annotated_frame = cv2.resize(annotated_frame, (original_width, original_height))
                
                # Update performance tracking
                frame_time = time.time() - frame_start
                self.frame_times.append(frame_time)
                self.processing_times.append(processing_time)
                
                # Calculate FPS
                if time.time() - last_fps_update >= 1.0:
                    current_fps = len(self.frame_times) / sum(self.frame_times) if self.frame_times else 0
                    self.fps_history.append(current_fps)
                    last_fps_update = time.time()
                    
                    # Keep only recent history
                    if len(self.fps_history) > 10:
                        self.fps_history.pop(0)
                    if len(self.frame_times) > 30:
                        self.frame_times = self.frame_times[-30:]
                        self.processing_times = self.processing_times[-30:]
                
                # Add performance overlay
                if self.show_performance_overlay:
                    annotated_frame = self._add_speed_overlay(annotated_frame, stats, processing_time)
                
                # Display frame
                cv2.imshow('SPEED-OPTIMIZED Person-Under-Load Safety System', annotated_frame)
                
                # Handle keyboard input
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    self._save_frame(annotated_frame, processed_count, stats)
                elif key == ord('p'):
                    self.show_performance_overlay = not self.show_performance_overlay
                    print(f"📊 Performance overlay: {'ON' if self.show_performance_overlay else 'OFF'}")
                elif key == ord('r'):
                    self._reset_performance_stats()
                elif key == ord('+') or key == ord('='):
                    self.frame_skip = min(self.frame_skip + 1, 10)
                    print(f"⏭️ Frame skip: {self.frame_skip}")
                elif key == ord('-'):
                    self.frame_skip = max(self.frame_skip - 1, 1)
                    print(f"⏭️ Frame skip: {self.frame_skip}")
                
                # Print periodic stats
                if processed_count % 100 == 0:
                    self._print_performance_summary(processed_count, time.time() - start_time)
        
        except KeyboardInterrupt:
            print("\n⏹️ Demo interrupted by user")
        
        finally:
            # Cleanup
            cap.release()
            cv2.destroyAllWindows()
            
            # Final performance report
            self._print_final_report(processed_count, time.time() - start_time)
    
    def _add_speed_overlay(self, frame, stats, processing_time):
        """Add speed-optimized performance overlay"""
        overlay = frame.copy()
        
        # Performance metrics
        current_fps = 1.0 / processing_time if processing_time > 0 else 0
        avg_fps = np.mean(self.fps_history) if self.fps_history else 0
        
        # Safety status
        safety_status = stats.get('safety_status', {})
        workers = safety_status.get('workers_detected', 0)
        loads = safety_status.get('loads_detected', 0)
        overall_status = safety_status.get('overall_status', 'UNKNOWN')
        
        # Create overlay text
        y_offset = 30
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        thickness = 2
        
        # Header
        cv2.putText(overlay, "SPEED-OPTIMIZED SYSTEM", (10, y_offset), 
                   font, 0.8, (0, 255, 0), thickness)
        y_offset += 40
        
        # Performance metrics
        cv2.putText(overlay, f"FPS: {current_fps:.1f} (avg: {avg_fps:.1f})", 
                   (10, y_offset), font, font_scale, (0, 255, 0), thickness)
        y_offset += 25
        
        cv2.putText(overlay, f"Processing: {processing_time*1000:.1f}ms", 
                   (10, y_offset), font, font_scale, (0, 255, 0), thickness)
        y_offset += 25
        
        # Detection stats
        cv2.putText(overlay, f"Workers: {workers} | Loads: {loads}", 
                   (10, y_offset), font, font_scale, (255, 255, 255), thickness)
        y_offset += 25
        
        # Safety status
        status_color = (0, 0, 255) if overall_status == 'DANGER' else (0, 255, 0)
        cv2.putText(overlay, f"Status: {overall_status}", 
                   (10, y_offset), font, font_scale, status_color, thickness)
        y_offset += 25
        
        # Speed optimizations active
        cv2.putText(overlay, "⚡ Edge Refinement: DISABLED", 
                   (10, y_offset), font, font_scale, (255, 255, 0), thickness)
        y_offset += 25
        
        cv2.putText(overlay, "⚡ Enhanced Mapping: DISABLED", 
                   (10, y_offset), font, font_scale, (255, 255, 0), thickness)
        y_offset += 25
        
        cv2.putText(overlay, f"⚡ Frame Skip: {self.frame_skip}", 
                   (10, y_offset), font, font_scale, (255, 255, 0), thickness)
        
        # Blend overlay
        alpha = 0.7
        cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0, frame)
        
        return frame
    
    def _save_frame(self, frame, frame_count, stats):
        """Save current frame with metadata"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"speed_frame_{timestamp}_{frame_count:06d}.jpg"
        cv2.imwrite(filename, frame)
        print(f"💾 Saved: {filename}")
    
    def _reset_performance_stats(self):
        """Reset performance statistics"""
        self.frame_times.clear()
        self.processing_times.clear()
        self.fps_history.clear()
        print("🔄 Performance stats reset")
    
    def _print_performance_summary(self, frame_count, elapsed_time):
        """Print performance summary"""
        avg_fps = frame_count / elapsed_time if elapsed_time > 0 else 0
        avg_processing = np.mean(self.processing_times) if self.processing_times else 0
        
        print(f"\n📊 Frame {frame_count} | "
              f"Avg FPS: {avg_fps:.1f} | "
              f"Avg Processing: {avg_processing*1000:.1f}ms")
    
    def _print_final_report(self, total_frames, total_time):
        """Print final performance report"""
        print("\n" + "=" * 60)
        print("📊 SPEED-OPTIMIZED DEMO - FINAL REPORT")
        print("=" * 60)
        
        if total_time > 0:
            avg_fps = total_frames / total_time
            print(f"🎥 Total frames processed: {total_frames}")
            print(f"⏱️ Total time: {total_time:.1f}s")
            print(f"📈 Average FPS: {avg_fps:.1f}")
        
        if self.processing_times:
            avg_processing = np.mean(self.processing_times)
            min_processing = np.min(self.processing_times)
            max_processing = np.max(self.processing_times)
            
            print(f"⚡ Processing time - Avg: {avg_processing*1000:.1f}ms | "
                  f"Min: {min_processing*1000:.1f}ms | "
                  f"Max: {max_processing*1000:.1f}ms")
        
        print(f"\n🚀 Speed Optimizations Applied:")
        print(f"   ⚡ Edge refinement disabled (saved ~118ms)")
        print(f"   ⚡ Enhanced mapping disabled (saved ~31ms)")
        print(f"   ⚡ Frame skipping: {self.frame_skip}")
        print(f"   ⚡ Resolution scaling: {self.resolution_scale}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Speed-Optimized Person-Under-Load Safety Demo')
    parser.add_argument('--video', type=str, default='data/test_videos/test2.mp4', 
                       help='Video source (0 for webcam, path for video file)')
    parser.add_argument('--resolution', type=float, default=0.75,
                       help='Resolution scale factor (0.5 = half resolution)')
    parser.add_argument('--skip', type=int, default=1,
                       help='Frame skip interval (2 = process every 2nd frame)')
    
    args = parser.parse_args()
    
    # Parse video source
    video_source = args.video
    if video_source.isdigit():
        video_source = int(video_source)
    
    # Create and run demo
    demo = SpeedOptimizedDemo(
        video_source=video_source, 
        resolution_scale=args.resolution,
        frame_skip=args.skip
    )
    demo.run()

if __name__ == "__main__":
    main()
