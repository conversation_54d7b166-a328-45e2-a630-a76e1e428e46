#!/usr/bin/env python3
"""
Test with detailed debugging to see what's happening
"""

import cv2
import numpy as np
from src.detection.yolo_detector import ConstructionSiteDetector
from src.detection.object_tracker import SafetyTracker
import supervision as sv

def test_with_debug(video_path: str, max_frames: int = 5):
    """Test with detailed debugging"""
    
    print(f"🔍 Testing with debug: {video_path}")
    
    # Initialize detector with very low threshold
    detector = ConstructionSiteDetector(confidence_threshold=0.05)
    tracker = SafetyTracker()
    
    cap = cv2.VideoCapture(video_path)
    
    frame_count = 0
    while frame_count < max_frames:
        ret, frame = cap.read()
        if not ret:
            break
        
        print(f"\n--- Frame {frame_count} ---")
        
        # Get raw detections
        detections = detector.detect_objects(frame)
        objects_info = detector.get_object_details(detections)
        
        print(f"Raw detections after filtering: {len(detections)}")
        
        # Show what we detected
        for obj in objects_info:
            print(f"  {obj['class_name']}: conf={obj['confidence']:.3f}, priority={obj['safety_priority']}")
        
        # Update tracker
        tracked_objects = tracker.update(detections, objects_info)
        
        # Check workers and loads
        workers = [obj for obj in tracked_objects if obj.class_name == 'person']
        loads = tracker.get_loads()

        print(f"After tracking:")
        print(f"  Workers: {len(workers)}")
        print(f"  Loads: {len(loads)}")
        print(f"  All tracked objects:")
        for obj in tracked_objects:
            print(f"    {obj.class_name} (ID: {obj.id}, conf: {obj.confidence:.3f})")

        for load in loads:
            print(f"    Load: {load.class_name} (conf: {load.confidence:.3f})")
        
        frame_count += 1
    
    cap.release()

def main():
    print("🔍 DETAILED DEBUG TEST")
    print("=" * 50)
    
    print("\n🎬 VIDEO 1: nachine-human.mp4")
    test_with_debug("data/test_videos/nachine-human.mp4", max_frames=3)
    
    print("\n🎬 VIDEO 2: test2.mp4")
    test_with_debug("data/test_videos/test2.mp4", max_frames=3)

if __name__ == "__main__":
    main()
