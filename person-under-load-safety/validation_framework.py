#!/usr/bin/env python3
"""
Validation Framework for 3D Spatial Data Accuracy
Ensures the Person-Under-Load Safety System provides accurate spatial measurements
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from src.main import PersonUnderLoadSystem
from src.geometry.coordinate_mapper import Position3D
import json
import os
from typing import List, Dict, Tuple
import time

class SpatialValidationFramework:
    """
    Comprehensive validation framework for 3D spatial accuracy
    """
    
    def __init__(self):
        self.system = PersonUnderLoadSystem()
        self.validation_results = {
            'distance_validation': [],
            'consistency_validation': [],
            'ground_truth_comparison': [],
            'temporal_stability': [],
            'geometric_validation': []
        }
        
        print("🔬 Spatial Validation Framework Initialized")
        print("📋 Validation Tests:")
        print("   1. Distance Measurement Accuracy")
        print("   2. Temporal Consistency Analysis") 
        print("   3. Geometric Relationship Validation")
        print("   4. Ground Truth Comparison")
        print("   5. Edge Refinement Quality Assessment")
    
    def validate_distance_measurements(self, video_path: str, known_distances: Dict = None) -> Dict:
        """
        Validate 3D distance measurements against known references
        
        Args:
            video_path: Path to test video
            known_distances: Dictionary of known real-world distances for validation
        """
        print("\n🔍 VALIDATION TEST 1: Distance Measurement Accuracy")
        print("-" * 60)
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return {"error": "Could not open video"}
        
        # Test specific frames for distance validation
        test_frames = [0, 15, 30, 45]
        distance_measurements = []
        
        for frame_num in test_frames:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
            ret, frame = cap.read()
            if not ret:
                continue
            
            # Process frame
            annotated_frame, stats = self.system.process_frame(frame)
            
            # Extract 3D positions
            spatial_stats = stats.get('spatial_stats', {})
            avg_distance = spatial_stats.get('avg_distance_from_camera', 0)
            
            distance_measurements.append({
                'frame': frame_num,
                'avg_distance': avg_distance,
                'num_objects': spatial_stats.get('3d_positions', 0)
            })
            
            print(f"Frame {frame_num}: {spatial_stats.get('3d_positions', 0)} objects, avg distance: {avg_distance:.1f}m")
        
        cap.release()
        
        # Analyze distance consistency
        distances = [d['avg_distance'] for d in distance_measurements if d['avg_distance'] > 0]
        if distances:
            distance_std = np.std(distances)
            distance_mean = np.mean(distances)
            consistency_score = 1.0 - (distance_std / distance_mean) if distance_mean > 0 else 0
            
            validation_result = {
                'measurements': distance_measurements,
                'mean_distance': distance_mean,
                'std_deviation': distance_std,
                'consistency_score': consistency_score,
                'validation_status': 'PASS' if consistency_score > 0.8 else 'NEEDS_REVIEW'
            }
            
            print(f"📊 Distance Analysis:")
            print(f"   Mean distance: {distance_mean:.1f}m")
            print(f"   Std deviation: {distance_std:.1f}m")
            print(f"   Consistency score: {consistency_score:.3f}")
            print(f"   Status: {validation_result['validation_status']}")
            
            self.validation_results['distance_validation'] = validation_result
            return validation_result
        
        return {"error": "No valid distance measurements"}
    
    def validate_temporal_consistency(self, video_path: str, sequence_length: int = 30) -> Dict:
        """
        Validate that 3D positions remain consistent across time for stationary objects
        """
        print("\n⏱️ VALIDATION TEST 2: Temporal Consistency Analysis")
        print("-" * 60)
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return {"error": "Could not open video"}
        
        # Track positions across consecutive frames
        position_history = []
        frame_count = 0
        
        while frame_count < sequence_length:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Process frame
            annotated_frame, stats = self.system.process_frame(frame)
            
            # Store spatial information
            spatial_stats = stats.get('spatial_stats', {})
            position_history.append({
                'frame': frame_count,
                'num_objects': spatial_stats.get('3d_positions', 0),
                'avg_distance': spatial_stats.get('avg_distance_from_camera', 0)
            })
            
            frame_count += 1
        
        cap.release()
        
        # Analyze temporal stability
        distances = [p['avg_distance'] for p in position_history if p['avg_distance'] > 0]
        object_counts = [p['num_objects'] for p in position_history]
        
        if len(distances) > 1:
            distance_variance = np.var(distances)
            count_variance = np.var(object_counts)
            
            # Calculate stability scores
            distance_stability = 1.0 / (1.0 + distance_variance) if distance_variance > 0 else 1.0
            count_stability = 1.0 / (1.0 + count_variance) if count_variance > 0 else 1.0
            
            validation_result = {
                'position_history': position_history,
                'distance_variance': distance_variance,
                'count_variance': count_variance,
                'distance_stability': distance_stability,
                'count_stability': count_stability,
                'overall_stability': (distance_stability + count_stability) / 2,
                'validation_status': 'PASS' if distance_stability > 0.7 and count_stability > 0.7 else 'NEEDS_REVIEW'
            }
            
            print(f"📊 Temporal Analysis:")
            print(f"   Distance variance: {distance_variance:.2f}")
            print(f"   Count variance: {count_variance:.2f}")
            print(f"   Distance stability: {distance_stability:.3f}")
            print(f"   Count stability: {count_stability:.3f}")
            print(f"   Overall stability: {validation_result['overall_stability']:.3f}")
            print(f"   Status: {validation_result['validation_status']}")
            
            self.validation_results['temporal_stability'] = validation_result
            return validation_result
        
        return {"error": "Insufficient data for temporal analysis"}
    
    def validate_geometric_relationships(self, video_path: str) -> Dict:
        """
        Validate that geometric relationships (relative positions, angles) are preserved
        """
        print("\n📐 VALIDATION TEST 3: Geometric Relationship Validation")
        print("-" * 60)
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return {"error": "Could not open video"}
        
        # Test geometric consistency across frames
        geometric_data = []
        
        for frame_num in [0, 15, 30]:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
            ret, frame = cap.read()
            if not ret:
                continue
            
            # Process frame
            annotated_frame, stats = self.system.process_frame(frame)
            
            # Analyze refinement quality
            refinement_stats = stats.get('refinement_stats', {})
            
            geometric_data.append({
                'frame': frame_num,
                'refined_objects': refinement_stats.get('refined_detections', 0),
                'edge_strength': refinement_stats.get('avg_edge_strength', 0),
                'geometric_confidence': refinement_stats.get('avg_geometric_confidence', 0)
            })
            
            print(f"Frame {frame_num}: {refinement_stats.get('refined_detections', 0)} refined, "
                  f"edge strength: {refinement_stats.get('avg_edge_strength', 0):.0f}, "
                  f"geo confidence: {refinement_stats.get('avg_geometric_confidence', 0):.3f}")
        
        cap.release()
        
        # Analyze geometric quality
        if geometric_data:
            edge_strengths = [g['edge_strength'] for g in geometric_data if g['edge_strength'] > 0]
            confidences = [g['geometric_confidence'] for g in geometric_data if g['geometric_confidence'] > 0]
            
            validation_result = {
                'geometric_data': geometric_data,
                'avg_edge_strength': np.mean(edge_strengths) if edge_strengths else 0,
                'avg_confidence': np.mean(confidences) if confidences else 0,
                'edge_consistency': 1.0 - (np.std(edge_strengths) / np.mean(edge_strengths)) if edge_strengths and np.mean(edge_strengths) > 0 else 0,
                'confidence_consistency': 1.0 - (np.std(confidences) / np.mean(confidences)) if confidences and np.mean(confidences) > 0 else 0,
                'validation_status': 'PASS' if np.mean(confidences) > 0.6 and np.mean(edge_strengths) > 1000 else 'NEEDS_REVIEW'
            }
            
            print(f"📊 Geometric Analysis:")
            print(f"   Avg edge strength: {validation_result['avg_edge_strength']:.0f}")
            print(f"   Avg confidence: {validation_result['avg_confidence']:.3f}")
            print(f"   Edge consistency: {validation_result['edge_consistency']:.3f}")
            print(f"   Confidence consistency: {validation_result['confidence_consistency']:.3f}")
            print(f"   Status: {validation_result['validation_status']}")
            
            self.validation_results['geometric_validation'] = validation_result
            return validation_result
        
        return {"error": "No geometric data collected"}
    
    def generate_validation_report(self, output_path: str = "validation_report.json"):
        """
        Generate comprehensive validation report
        """
        print("\n📋 GENERATING VALIDATION REPORT")
        print("-" * 60)
        
        # Calculate overall validation score
        validation_scores = []
        
        for test_name, results in self.validation_results.items():
            if isinstance(results, dict) and 'validation_status' in results:
                score = 1.0 if results['validation_status'] == 'PASS' else 0.5
                validation_scores.append(score)
                print(f"   {test_name}: {results['validation_status']}")
        
        overall_score = np.mean(validation_scores) if validation_scores else 0
        overall_status = "VALIDATED" if overall_score >= 0.8 else "NEEDS_IMPROVEMENT"
        
        # Create comprehensive report
        report = {
            'validation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'overall_score': overall_score,
            'overall_status': overall_status,
            'test_results': self.validation_results,
            'summary': {
                'tests_passed': sum(1 for score in validation_scores if score == 1.0),
                'tests_total': len(validation_scores),
                'pass_rate': overall_score,
                'recommendations': self._generate_recommendations()
            }
        }
        
        # Save report
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n📊 VALIDATION SUMMARY:")
        print(f"   Overall Score: {overall_score:.2f}")
        print(f"   Overall Status: {overall_status}")
        print(f"   Tests Passed: {report['summary']['tests_passed']}/{report['summary']['tests_total']}")
        print(f"   Report saved: {output_path}")
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        # Check distance validation
        if 'distance_validation' in self.validation_results:
            result = self.validation_results['distance_validation']
            if isinstance(result, dict) and result.get('consistency_score', 0) < 0.8:
                recommendations.append("Improve camera calibration for better distance accuracy")
        
        # Check temporal stability
        if 'temporal_stability' in self.validation_results:
            result = self.validation_results['temporal_stability']
            if isinstance(result, dict) and result.get('overall_stability', 0) < 0.7:
                recommendations.append("Enhance tracking algorithms for better temporal consistency")
        
        # Check geometric validation
        if 'geometric_validation' in self.validation_results:
            result = self.validation_results['geometric_validation']
            if isinstance(result, dict) and result.get('avg_confidence', 0) < 0.6:
                recommendations.append("Tune LoG parameters for better edge detection")
        
        if not recommendations:
            recommendations.append("System validation successful - ready for deployment")
        
        return recommendations

def main():
    """Run comprehensive validation suite"""
    
    print("🔬 SPATIAL VALIDATION FRAMEWORK")
    print("=" * 80)
    print("🎯 Validating 3D Spatial Data Accuracy for Life-Safety System")
    print("=" * 80)
    
    validator = SpatialValidationFramework()
    video_path = "data/test_videos/test2.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ Test video not found: {video_path}")
        return
    
    # Run validation tests
    print("🚀 Running validation test suite...")
    
    # Test 1: Distance measurements
    validator.validate_distance_measurements(video_path)
    
    # Test 2: Temporal consistency
    validator.validate_temporal_consistency(video_path, sequence_length=20)
    
    # Test 3: Geometric relationships
    validator.validate_geometric_relationships(video_path)
    
    # Generate final report
    report = validator.generate_validation_report("spatial_validation_report.json")
    
    print(f"\n🎯 VALIDATION COMPLETE")
    print(f"📋 System Status: {report['overall_status']}")
    print(f"🛡️ Ready for life-safety deployment: {'YES' if report['overall_status'] == 'VALIDATED' else 'NEEDS_IMPROVEMENT'}")

if __name__ == "__main__":
    main()
