#!/usr/bin/env python3
"""
Week 3 Enhanced Testing Suite
Validates DepthAnything-AC integration and performance improvements

Tests:
1. Depth estimation accuracy
2. Temporal smoothing effectiveness
3. Performance optimization validation
4. Distance calibration improvement
"""

import cv2
import numpy as np
import time
import sys
from pathlib import Path
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from src.main import PersonUnderLoadSystem
from src.geometry.depth_anything_integration import DepthAnythingACIntegration
from src.geometry.enhanced_spatial_mapper import EnhancedSpatialMapper
from src.geometry.coordinate_mapper import CameraCalibration

class Week3TestSuite:
    """Comprehensive testing suite for Week 3 enhancements"""
    
    def __init__(self):
        """Initialize test suite"""
        print("🧪 Week 3 Enhanced Testing Suite")
        print("=" * 50)
        
        # Initialize systems for comparison
        self.enhanced_system = PersonUnderLoadSystem(use_enhanced_mapping=True)
        self.standard_system = PersonUnderLoadSystem(use_enhanced_mapping=False)
        
        # Test data storage
        self.test_results = {
            'depth_accuracy': [],
            'temporal_stability': [],
            'performance_comparison': [],
            'distance_calibration': []
        }
        
        print("✅ Enhanced system initialized")
        print("✅ Standard system initialized")
        print("✅ Test data structures ready")
    
    def run_all_tests(self, test_video_path: str = None):
        """Run all Week 3 tests"""
        print("\n🚀 Starting Week 3 comprehensive test suite...")
        
        # Test 1: Depth estimation accuracy
        print("\n" + "="*50)
        print("🔍 TEST 1: Depth Estimation Accuracy")
        print("="*50)
        self.test_depth_estimation_accuracy(test_video_path)
        
        # Test 2: Temporal smoothing effectiveness
        print("\n" + "="*50)
        print("⏱️ TEST 2: Temporal Smoothing Effectiveness")
        print("="*50)
        self.test_temporal_smoothing(test_video_path)
        
        # Test 3: Performance comparison
        print("\n" + "="*50)
        print("⚡ TEST 3: Performance Optimization")
        print("="*50)
        self.test_performance_optimization(test_video_path)
        
        # Test 4: Distance calibration improvement
        print("\n" + "="*50)
        print("📐 TEST 4: Distance Calibration Improvement")
        print("="*50)
        self.test_distance_calibration(test_video_path)
        
        # Generate comprehensive report
        self.generate_test_report()
    
    def test_depth_estimation_accuracy(self, test_video_path: str = None):
        """Test depth estimation accuracy"""
        print("Testing DepthAnything-AC depth estimation...")
        
        # Create test frame
        test_frame = self._create_test_frame()
        
        # Initialize depth estimator
        depth_estimator = DepthAnythingACIntegration()
        
        # Test depth estimation
        start_time = time.time()
        depth_result = depth_estimator.estimate_depth_robust(test_frame)
        processing_time = time.time() - start_time
        
        # Analyze results
        depth_map = depth_result.depth_map
        confidence_map = depth_result.confidence_map
        
        # Calculate metrics
        depth_range = (np.min(depth_map), np.max(depth_map))
        avg_confidence = np.mean(confidence_map)
        spatial_consistency = depth_result.spatial_consistency_score
        
        print(f"✅ Depth estimation completed in {processing_time*1000:.1f}ms")
        print(f"📏 Depth range: {depth_range[0]:.1f}m - {depth_range[1]:.1f}m")
        print(f"🎯 Average confidence: {avg_confidence:.3f}")
        print(f"📊 Spatial consistency: {spatial_consistency:.3f}")
        
        # Store results
        self.test_results['depth_accuracy'].append({
            'processing_time': processing_time,
            'depth_range': depth_range,
            'avg_confidence': avg_confidence,
            'spatial_consistency': spatial_consistency
        })
        
        # Validate depth map quality
        if spatial_consistency > 0.5:
            print("✅ PASS: Spatial consistency meets threshold")
        else:
            print("❌ FAIL: Spatial consistency below threshold")
        
        if avg_confidence > 0.6:
            print("✅ PASS: Average confidence meets threshold")
        else:
            print("❌ FAIL: Average confidence below threshold")
    
    def test_temporal_smoothing(self, test_video_path: str = None):
        """Test temporal smoothing effectiveness"""
        print("Testing Kalman filtering temporal smoothing...")
        
        # Create camera calibration
        camera_cal = CameraCalibration(
            focal_length=800,
            principal_point=(960, 540),
            image_size=(1920, 1080),
            height_above_ground=5.0,
            tilt_angle=np.radians(15)
        )
        
        # Initialize enhanced spatial mapper
        enhanced_mapper = EnhancedSpatialMapper(camera_cal)
        
        # Create sequence of test frames with simulated movement
        test_frames = self._create_test_sequence()
        
        positions_without_smoothing = []
        positions_with_smoothing = []
        
        for i, frame in enumerate(test_frames):
            # Simulate detection at moving position
            detection = self._create_mock_detection(i)
            
            # Test with enhanced mapper (includes Kalman smoothing)
            enhanced_positions = enhanced_mapper.map_detections_to_3d_enhanced([detection], frame)
            
            if enhanced_positions:
                positions_with_smoothing.append(enhanced_positions[0])
            
            # Simulate raw position (without smoothing)
            raw_position = self._simulate_raw_position(i)
            positions_without_smoothing.append(raw_position)
        
        # Calculate smoothness metrics
        smoothness_raw = self._calculate_smoothness(positions_without_smoothing)
        smoothness_enhanced = self._calculate_smoothness(positions_with_smoothing)
        
        improvement = (smoothness_enhanced - smoothness_raw) / smoothness_raw * 100
        
        print(f"📊 Raw position smoothness: {smoothness_raw:.3f}")
        print(f"📊 Enhanced smoothness: {smoothness_enhanced:.3f}")
        print(f"📈 Improvement: {improvement:.1f}%")
        
        # Store results
        self.test_results['temporal_stability'].append({
            'smoothness_raw': smoothness_raw,
            'smoothness_enhanced': smoothness_enhanced,
            'improvement_percent': improvement
        })
        
        if improvement > 20:
            print("✅ PASS: Temporal smoothing shows significant improvement")
        else:
            print("❌ FAIL: Temporal smoothing improvement below threshold")
    
    def test_performance_optimization(self, test_video_path: str = None):
        """Test performance optimization"""
        print("Testing performance improvements...")
        
        test_frame = self._create_test_frame()
        num_iterations = 10
        
        # Test enhanced system performance
        enhanced_times = []
        for _ in range(num_iterations):
            start_time = time.time()
            _, _ = self.enhanced_system.process_frame(test_frame)
            enhanced_times.append(time.time() - start_time)
        
        # Test standard system performance
        standard_times = []
        for _ in range(num_iterations):
            start_time = time.time()
            _, _ = self.standard_system.process_frame(test_frame)
            standard_times.append(time.time() - start_time)
        
        # Calculate metrics
        avg_enhanced = np.mean(enhanced_times)
        avg_standard = np.mean(standard_times)
        
        enhanced_fps = 1.0 / avg_enhanced
        standard_fps = 1.0 / avg_standard
        
        fps_improvement = (enhanced_fps - standard_fps) / standard_fps * 100
        
        print(f"⚡ Enhanced system: {avg_enhanced*1000:.1f}ms avg ({enhanced_fps:.1f} FPS)")
        print(f"⚡ Standard system: {avg_standard*1000:.1f}ms avg ({standard_fps:.1f} FPS)")
        print(f"📈 FPS improvement: {fps_improvement:.1f}%")
        
        # Store results
        self.test_results['performance_comparison'].append({
            'enhanced_avg_time': avg_enhanced,
            'standard_avg_time': avg_standard,
            'enhanced_fps': enhanced_fps,
            'standard_fps': standard_fps,
            'fps_improvement': fps_improvement
        })
        
        # Target: 15+ FPS for enhanced system
        if enhanced_fps >= 15.0:
            print("✅ PASS: Enhanced system achieves 15+ FPS target")
        else:
            print(f"⚠️ PARTIAL: Enhanced system FPS ({enhanced_fps:.1f}) below 15 FPS target")
        
        # Performance should not degrade significantly
        if fps_improvement > -10:
            print("✅ PASS: Performance maintained or improved")
        else:
            print("❌ FAIL: Significant performance degradation")
    
    def test_distance_calibration(self, test_video_path: str = None):
        """Test distance calibration improvement"""
        print("Testing distance calibration accuracy...")
        
        # Create test scenarios with known distances
        test_scenarios = [
            {'pixel_pos': (960, 700), 'expected_distance': 10.0},  # Close
            {'pixel_pos': (960, 600), 'expected_distance': 20.0},  # Medium
            {'pixel_pos': (960, 500), 'expected_distance': 30.0},  # Far
        ]
        
        test_frame = self._create_test_frame()
        
        enhanced_errors = []
        standard_errors = []
        
        for scenario in test_scenarios:
            # Test enhanced system
            enhanced_positions, _ = self.enhanced_system.process_frame(test_frame)
            if enhanced_positions:
                enhanced_distance = np.sqrt(enhanced_positions[0].x**2 + enhanced_positions[0].y**2)
                enhanced_error = abs(enhanced_distance - scenario['expected_distance'])
                enhanced_errors.append(enhanced_error)
            
            # Test standard system
            standard_positions, _ = self.standard_system.process_frame(test_frame)
            if standard_positions:
                standard_distance = np.sqrt(standard_positions[0].x**2 + standard_positions[0].y**2)
                standard_error = abs(standard_distance - scenario['expected_distance'])
                standard_errors.append(standard_error)
        
        # Calculate accuracy metrics
        avg_enhanced_error = np.mean(enhanced_errors) if enhanced_errors else float('inf')
        avg_standard_error = np.mean(standard_errors) if standard_errors else float('inf')
        
        accuracy_improvement = (avg_standard_error - avg_enhanced_error) / avg_standard_error * 100
        
        print(f"📏 Enhanced system avg error: {avg_enhanced_error:.1f}m")
        print(f"📏 Standard system avg error: {avg_standard_error:.1f}m")
        print(f"📈 Accuracy improvement: {accuracy_improvement:.1f}%")
        
        # Store results
        self.test_results['distance_calibration'].append({
            'enhanced_avg_error': avg_enhanced_error,
            'standard_avg_error': avg_standard_error,
            'accuracy_improvement': accuracy_improvement
        })
        
        if accuracy_improvement > 30:
            print("✅ PASS: Significant distance accuracy improvement")
        elif accuracy_improvement > 0:
            print("⚠️ PARTIAL: Some distance accuracy improvement")
        else:
            print("❌ FAIL: No distance accuracy improvement")
    
    def _create_test_frame(self) -> np.ndarray:
        """Create a synthetic test frame"""
        frame = np.zeros((1080, 1920, 3), dtype=np.uint8)
        
        # Add some texture and objects
        cv2.rectangle(frame, (400, 400), (600, 600), (100, 100, 100), -1)
        cv2.rectangle(frame, (800, 300), (1000, 500), (150, 150, 150), -1)
        cv2.circle(frame, (960, 540), 50, (200, 200, 200), -1)
        
        # Add noise for texture
        noise = np.random.randint(0, 50, frame.shape, dtype=np.uint8)
        frame = cv2.add(frame, noise)
        
        return frame
    
    def _create_test_sequence(self) -> List[np.ndarray]:
        """Create a sequence of test frames"""
        frames = []
        for i in range(10):
            frame = self._create_test_frame()
            # Add moving object
            x = 400 + i * 20
            y = 400 + i * 10
            cv2.circle(frame, (x, y), 30, (255, 255, 255), -1)
            frames.append(frame)
        return frames
    
    def _create_mock_detection(self, frame_index: int):
        """Create a mock detection for testing"""
        class MockDetection:
            def __init__(self, x, y):
                self.bbox = type('bbox', (), {
                    'x': x, 'y': y, 'width': 50, 'height': 50
                })()
                self.id = 1
        
        x = 400 + frame_index * 20
        y = 400 + frame_index * 10
        return MockDetection(x, y)
    
    def _simulate_raw_position(self, frame_index: int):
        """Simulate raw position with noise"""
        from src.geometry.coordinate_mapper import Position3D
        
        # Base position with movement
        base_x = 5.0 + frame_index * 0.5
        base_y = 10.0 + frame_index * 0.3
        base_z = 0.0
        
        # Add noise
        noise_x = np.random.normal(0, 0.2)
        noise_y = np.random.normal(0, 0.2)
        noise_z = np.random.normal(0, 0.1)
        
        return Position3D(
            x=base_x + noise_x,
            y=base_y + noise_y,
            z=base_z + noise_z
        )
    
    def _calculate_smoothness(self, positions) -> float:
        """Calculate smoothness metric for position sequence"""
        if len(positions) < 2:
            return 0.0
        
        # Calculate velocity changes (acceleration)
        accelerations = []
        for i in range(2, len(positions)):
            v1_x = positions[i-1].x - positions[i-2].x
            v1_y = positions[i-1].y - positions[i-2].y
            
            v2_x = positions[i].x - positions[i-1].x
            v2_y = positions[i].y - positions[i-1].y
            
            acc_x = v2_x - v1_x
            acc_y = v2_y - v1_y
            
            acceleration_magnitude = np.sqrt(acc_x**2 + acc_y**2)
            accelerations.append(acceleration_magnitude)
        
        # Smoothness is inverse of acceleration variance
        if accelerations:
            smoothness = 1.0 / (1.0 + np.var(accelerations))
        else:
            smoothness = 1.0
        
        return smoothness
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*60)
        print("📊 WEEK 3 ENHANCED TESTING - COMPREHENSIVE REPORT")
        print("="*60)
        
        # Overall assessment
        total_tests = 0
        passed_tests = 0
        
        # Depth accuracy assessment
        if self.test_results['depth_accuracy']:
            result = self.test_results['depth_accuracy'][0]
            print(f"\n🔍 DEPTH ESTIMATION ACCURACY:")
            print(f"   Processing time: {result['processing_time']*1000:.1f}ms")
            print(f"   Spatial consistency: {result['spatial_consistency']:.3f}")
            print(f"   Average confidence: {result['avg_confidence']:.3f}")
            
            if result['spatial_consistency'] > 0.5 and result['avg_confidence'] > 0.6:
                print("   ✅ PASS")
                passed_tests += 1
            else:
                print("   ❌ FAIL")
            total_tests += 1
        
        # Temporal smoothing assessment
        if self.test_results['temporal_stability']:
            result = self.test_results['temporal_stability'][0]
            print(f"\n⏱️ TEMPORAL SMOOTHING:")
            print(f"   Improvement: {result['improvement_percent']:.1f}%")
            
            if result['improvement_percent'] > 20:
                print("   ✅ PASS")
                passed_tests += 1
            else:
                print("   ❌ FAIL")
            total_tests += 1
        
        # Performance assessment
        if self.test_results['performance_comparison']:
            result = self.test_results['performance_comparison'][0]
            print(f"\n⚡ PERFORMANCE OPTIMIZATION:")
            print(f"   Enhanced FPS: {result['enhanced_fps']:.1f}")
            print(f"   FPS improvement: {result['fps_improvement']:.1f}%")
            
            if result['enhanced_fps'] >= 15.0 and result['fps_improvement'] > -10:
                print("   ✅ PASS")
                passed_tests += 1
            else:
                print("   ❌ FAIL")
            total_tests += 1
        
        # Distance calibration assessment
        if self.test_results['distance_calibration']:
            result = self.test_results['distance_calibration'][0]
            print(f"\n📐 DISTANCE CALIBRATION:")
            print(f"   Accuracy improvement: {result['accuracy_improvement']:.1f}%")
            
            if result['accuracy_improvement'] > 0:
                print("   ✅ PASS")
                passed_tests += 1
            else:
                print("   ❌ FAIL")
            total_tests += 1
        
        # Final assessment
        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"\n🎯 OVERALL ASSESSMENT:")
        print(f"   Tests passed: {passed_tests}/{total_tests}")
        print(f"   Pass rate: {pass_rate:.1f}%")
        
        if pass_rate >= 75:
            print("   🏆 EXCELLENT: Week 3 enhancements working excellently!")
        elif pass_rate >= 50:
            print("   ✅ GOOD: Week 3 enhancements showing improvement")
        else:
            print("   ⚠️ NEEDS WORK: Week 3 enhancements need refinement")
        
        print("\n🛡️ Week 3 testing complete!")

def main():
    """Main function"""
    test_suite = Week3TestSuite()
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()
