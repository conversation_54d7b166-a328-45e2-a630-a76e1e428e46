#!/usr/bin/env python3
"""
Quick demo to show detection results on test2 video with bounding boxes
"""

import cv2
import numpy as np
from src.main import PersonUnderLoadSystem
import os

def demo_test2_video():
    """Demo the system on test2 video and save annotated frames"""
    
    video_path = "data/test_videos/test2.mp4"
    
    print("🎬 DEMO: Person-Under-Load Detection on test2.mp4")
    print("=" * 60)
    
    if not os.path.exists(video_path):
        print(f"❌ Video not found: {video_path}")
        return
    
    # Initialize system
    print("🚀 Initializing detection system...")
    system = PersonUnderLoadSystem()
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"❌ Could not open video: {video_path}")
        return
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📊 Video Info:")
    print(f"   - Resolution: {width}x{height}")
    print(f"   - FPS: {fps}")
    print(f"   - Total frames: {total_frames}")
    print(f"   - Duration: {total_frames/fps:.1f} seconds")
    
    # Create output directory
    output_dir = "demo_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Process and save a few key frames
    frames_to_save = [0, 10, 20, 30, 40]  # Save every 10th frame for first 50 frames
    
    print(f"\n🔍 Processing and saving frames: {frames_to_save}")
    print("-" * 60)
    
    frame_count = 0
    saved_count = 0
    
    while frame_count <= max(frames_to_save):
        ret, frame = cap.read()
        if not ret:
            break
        
        if frame_count in frames_to_save:
            print(f"📸 Processing frame {frame_count}...")
            
            # Process frame through our system
            annotated_frame, stats = system.process_frame(frame)
            
            # Extract results
            safety_status = stats['safety_status']
            workers = safety_status['workers_detected']
            loads = safety_status['loads_detected']
            warnings = len(safety_status['warnings'])
            overall_status = safety_status['overall_status']
            
            # Save annotated frame
            output_filename = f"frame_{frame_count:03d}_workers_{workers}_loads_{loads}.jpg"
            output_path = os.path.join(output_dir, output_filename)
            
            # Add frame info text overlay
            info_text = f"Frame {frame_count} | Workers: {workers} | Loads: {loads} | Status: {overall_status}"
            cv2.putText(annotated_frame, info_text, (10, height - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(annotated_frame, info_text, (10, height - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 1)
            
            cv2.imwrite(output_path, annotated_frame)
            
            print(f"   ✅ Saved: {output_filename}")
            print(f"      - Workers detected: {workers}")
            print(f"      - Loads detected: {loads}")
            print(f"      - Warnings: {warnings}")
            print(f"      - Overall status: {overall_status}")
            
            saved_count += 1
        
        frame_count += 1
    
    cap.release()
    
    print(f"\n🎉 Demo completed!")
    print(f"📁 Saved {saved_count} annotated frames to: {output_dir}/")
    print(f"📋 Files saved:")
    
    # List saved files
    for filename in sorted(os.listdir(output_dir)):
        if filename.endswith('.jpg'):
            filepath = os.path.join(output_dir, filename)
            file_size = os.path.getsize(filepath) / (1024*1024)  # MB
            print(f"   - {filename} ({file_size:.1f} MB)")
    
    print(f"\n🔍 You can now view these images to see the detection results!")
    print(f"💡 Each image shows:")
    print(f"   - Green boxes around detected workers")
    print(f"   - Object IDs and confidence scores")
    print(f"   - Safety status information")
    print(f"   - Frame statistics at the bottom")

if __name__ == "__main__":
    demo_test2_video()
