import numpy as np
import cv2
from typing import <PERSON><PERSON>, Dict, List, Optional
from dataclasses import dataclass
from src.geometry.coordinate_mapper import CameraCalibration, Position3D

@dataclass
class CalibrationPoint:
    """Known calibration point with pixel and world coordinates"""
    pixel_coords: Tuple[float, float]
    world_coords: Tuple[float, float, float]
    confidence: float = 1.0

class ImprovedCameraCalibration:
    """
    Improved camera calibration system for accurate 3D spatial mapping
    Addresses the distance consistency issues identified in validation
    """
    
    def __init__(self):
        self.calibration_points: List[CalibrationPoint] = []
        self.homography_matrix: Optional[np.ndarray] = None
        self.camera_matrix: Optional[np.ndarray] = None
        self.distance_scale_factor: float = 1.0
        
        print("📐 Improved Camera Calibration System Initialized")
    
    def add_reference_points_from_video_analysis(self, video_path: str) -> Dict:
        """
        Analyze video to establish reference points for calibration
        Uses typical construction site assumptions
        """
        print("🔍 Analyzing video for calibration reference points...")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return {"error": "Could not open video"}
        
        # Get video properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Establish reference points based on construction site assumptions
        reference_points = [
            # Ground plane reference points (assuming flat construction site)
            CalibrationPoint(
                pixel_coords=(width * 0.1, height * 0.9),  # Bottom left
                world_coords=(0, 0, 0),  # Origin on ground
                confidence=0.9
            ),
            CalibrationPoint(
                pixel_coords=(width * 0.9, height * 0.9),  # Bottom right  
                world_coords=(20, 0, 0),  # 20m to the right
                confidence=0.9
            ),
            CalibrationPoint(
                pixel_coords=(width * 0.5, height * 0.7),  # Middle distance
                world_coords=(10, 15, 0),  # 15m forward, 10m right
                confidence=0.8
            ),
            CalibrationPoint(
                pixel_coords=(width * 0.5, height * 0.5),  # Far distance
                world_coords=(10, 30, 0),  # 30m forward, 10m right
                confidence=0.7
            ),
        ]
        
        self.calibration_points.extend(reference_points)
        cap.release()
        
        print(f"✅ Added {len(reference_points)} reference points")
        return {"reference_points": len(reference_points)}
    
    def calculate_improved_homography(self) -> bool:
        """
        Calculate homography matrix from reference points
        """
        if len(self.calibration_points) < 4:
            print("❌ Need at least 4 calibration points")
            return False
        
        # Extract pixel and world coordinates
        pixel_points = np.array([cp.pixel_coords for cp in self.calibration_points], dtype=np.float32)
        world_points = np.array([(cp.world_coords[0], cp.world_coords[1]) for cp in self.calibration_points], dtype=np.float32)
        
        # Calculate homography
        self.homography_matrix, mask = cv2.findHomography(
            pixel_points, world_points, 
            cv2.RANSAC, 5.0
        )
        
        if self.homography_matrix is not None:
            print("✅ Homography matrix calculated successfully")
            return True
        else:
            print("❌ Failed to calculate homography matrix")
            return False
    
    def create_adaptive_calibration(self, video_analysis_results: Dict) -> CameraCalibration:
        """
        Create adaptive camera calibration based on video analysis and validation results
        """
        print("🔧 Creating adaptive camera calibration...")
        
        # Analyze distance variance from validation results
        distance_measurements = video_analysis_results.get('distance_measurements', [])
        
        if distance_measurements:
            distances = [d['avg_distance'] for d in distance_measurements if d['avg_distance'] > 0]
            
            # Detect outliers (like the 268m measurement in frame 0)
            if distances:
                median_distance = np.median(distances)
                filtered_distances = [d for d in distances if abs(d - median_distance) < median_distance * 0.5]
                
                if filtered_distances:
                    typical_distance = np.mean(filtered_distances)
                    
                    # Adjust calibration based on typical distances
                    if typical_distance < 50:  # Close-range construction site
                        focal_length = 1200  # Higher focal length for close work
                        height_above_ground = 3.0  # Lower camera height
                        tilt_angle = np.radians(20)  # Steeper angle
                    else:  # Long-range construction site
                        focal_length = 600   # Lower focal length for wide view
                        height_above_ground = 8.0   # Higher camera height
                        tilt_angle = np.radians(10)  # Shallower angle
                else:
                    # Default values
                    focal_length = 800
                    height_above_ground = 5.0
                    tilt_angle = np.radians(15)
            else:
                # Default values
                focal_length = 800
                height_above_ground = 5.0
                tilt_angle = np.radians(15)
        else:
            # Default values
            focal_length = 800
            height_above_ground = 5.0
            tilt_angle = np.radians(15)
        
        # Create improved calibration
        improved_calibration = CameraCalibration(
            focal_length=focal_length,
            principal_point=(960, 540),  # Center of 1920x1080
            image_size=(1920, 1080),
            height_above_ground=height_above_ground,
            tilt_angle=tilt_angle
        )
        
        print(f"📐 Adaptive calibration created:")
        print(f"   Focal length: {focal_length}px")
        print(f"   Camera height: {height_above_ground}m")
        print(f"   Tilt angle: {np.degrees(tilt_angle):.1f}°")
        
        return improved_calibration
    
    def validate_calibration_accuracy(self, test_measurements: List[Dict]) -> Dict:
        """
        Validate calibration accuracy against test measurements
        """
        print("🔍 Validating calibration accuracy...")
        
        if not test_measurements:
            return {"error": "No test measurements provided"}
        
        # Calculate consistency metrics
        distances = [m['avg_distance'] for m in test_measurements if m['avg_distance'] > 0]
        
        if len(distances) < 2:
            return {"error": "Need at least 2 distance measurements"}
        
        # Remove outliers for consistency calculation
        median_distance = np.median(distances)
        filtered_distances = [d for d in distances if abs(d - median_distance) < median_distance * 0.3]
        
        if filtered_distances:
            consistency_std = np.std(filtered_distances)
            consistency_mean = np.mean(filtered_distances)
            consistency_score = 1.0 - (consistency_std / consistency_mean) if consistency_mean > 0 else 0
            
            validation_result = {
                'original_distances': distances,
                'filtered_distances': filtered_distances,
                'outliers_removed': len(distances) - len(filtered_distances),
                'consistency_mean': consistency_mean,
                'consistency_std': consistency_std,
                'consistency_score': consistency_score,
                'validation_status': 'PASS' if consistency_score > 0.8 else 'IMPROVED' if consistency_score > 0.5 else 'NEEDS_WORK'
            }
            
            print(f"📊 Calibration validation:")
            print(f"   Outliers removed: {validation_result['outliers_removed']}")
            print(f"   Consistency mean: {consistency_mean:.1f}m")
            print(f"   Consistency std: {consistency_std:.1f}m")
            print(f"   Consistency score: {consistency_score:.3f}")
            print(f"   Status: {validation_result['validation_status']}")
            
            return validation_result
        
        return {"error": "All measurements were outliers"}

class TemporalStabilityImprover:
    """
    Improves temporal stability of 3D position tracking
    """
    
    def __init__(self, smoothing_factor: float = 0.7):
        self.smoothing_factor = smoothing_factor
        self.position_history: Dict[int, List[Position3D]] = {}
        
    def stabilize_position(self, object_id: int, new_position: Position3D) -> Position3D:
        """
        Apply temporal smoothing to reduce position jitter
        """
        if object_id not in self.position_history:
            self.position_history[object_id] = []
        
        history = self.position_history[object_id]
        history.append(new_position)
        
        # Keep only recent history
        if len(history) > 5:
            history.pop(0)
        
        # Apply exponential smoothing
        if len(history) == 1:
            return new_position
        
        prev_position = history[-2]
        
        # Smooth coordinates
        smoothed_x = self.smoothing_factor * prev_position.x + (1 - self.smoothing_factor) * new_position.x
        smoothed_y = self.smoothing_factor * prev_position.y + (1 - self.smoothing_factor) * new_position.y
        smoothed_z = self.smoothing_factor * prev_position.z + (1 - self.smoothing_factor) * new_position.z
        
        # Create stabilized position
        stabilized_position = Position3D(
            x=smoothed_x,
            y=smoothed_y,
            z=smoothed_z,
            confidence=new_position.confidence,
            pixel_coords=new_position.pixel_coords
        )
        
        return stabilized_position

def create_improved_calibration_from_validation(validation_report_path: str) -> CameraCalibration:
    """
    Create improved calibration based on validation report
    """
    import json
    
    print("🔧 Creating improved calibration from validation results...")
    
    try:
        with open(validation_report_path, 'r') as f:
            report = json.load(f)
        
        # Extract distance measurements
        distance_validation = report.get('test_results', {}).get('distance_validation', {})
        measurements = distance_validation.get('measurements', [])
        
        # Create improved calibration
        calibrator = ImprovedCameraCalibration()
        improved_cal = calibrator.create_adaptive_calibration({'distance_measurements': measurements})
        
        # Validate the improvement
        validation_result = calibrator.validate_calibration_accuracy(measurements)
        
        print(f"✅ Improved calibration created with status: {validation_result.get('validation_status', 'UNKNOWN')}")
        
        return improved_cal
        
    except Exception as e:
        print(f"❌ Error creating improved calibration: {e}")
        # Return default calibration
        return CameraCalibration(
            focal_length=800,
            principal_point=(960, 540),
            image_size=(1920, 1080),
            height_above_ground=5.0,
            tilt_angle=np.radians(15)
        )
