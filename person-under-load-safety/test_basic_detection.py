#!/usr/bin/env python3
"""
Quick test script to verify the YOLO detection system works
"""

from src.detection.yolo_detector import ConstructionSiteDetector
import cv2
import numpy as np

def test_detector():
    """Test the detector with a simple image"""
    
    print("🧪 Testing YOLO Detector...")
    
    # Initialize detector
    try:
        detector = ConstructionSiteDetector()
        print("✅ Detector initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize detector: {e}")
        return False
    
    # Test with webcam frame
    print("📷 Attempting to capture test frame...")
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("❌ Could not open webcam")
        # Create a dummy test image instead
        print("🎨 Creating dummy test image...")
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.rectangle(frame, (100, 100), (200, 300), (255, 255, 255), -1)  # White rectangle (person-like)
        cv2.rectangle(frame, (400, 50), (500, 150), (128, 128, 128), -1)   # Gray rectangle (object-like)
    else:
        ret, frame = cap.read()
        cap.release()
        
        if not ret:
            print("❌ Could not capture frame")
            return False
        
        print("✅ Frame captured successfully")
    
    # Test detection
    try:
        print("🔍 Running detection...")
        detections = detector.detect_objects(frame)
        objects = detector.get_object_details(detections)
        
        print(f"✅ Detection completed!")
        print(f"📊 Results:")
        print(f"   - Detected {len(objects)} objects")
        
        for obj in objects:
            print(f"   - {obj['class_name']}: confidence {obj['confidence']:.2f}")
        
        # Test performance stats
        stats = detector.get_performance_stats()
        print(f"⚡ Performance:")
        for key, value in stats.items():
            print(f"   - {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Detection failed: {e}")
        return False

def test_full_pipeline():
    """Test the complete pipeline"""
    
    print("\n🔧 Testing Full Pipeline...")
    
    try:
        from src.main import PersonUnderLoadSystem
        
        system = PersonUnderLoadSystem()
        print("✅ Full system initialized")
        
        # Create test frame
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.rectangle(frame, (100, 100), (200, 300), (255, 255, 255), -1)
        
        # Process frame
        annotated_frame, stats = system.process_frame(frame)
        
        print("✅ Frame processed successfully")
        print(f"📊 Pipeline Stats:")
        print(f"   - Safety Status: {stats['safety_status']['overall_status']}")
        print(f"   - Workers: {stats['safety_status']['workers_detected']}")
        print(f"   - Loads: {stats['safety_status']['loads_detected']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Person-Under-Load Safety System - Basic Tests")
    print("=" * 50)
    
    # Test 1: Basic detector
    test1_passed = test_detector()
    
    # Test 2: Full pipeline
    test2_passed = test_full_pipeline()
    
    print("\n" + "=" * 50)
    print("📋 Test Results:")
    print(f"   Detector Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Pipeline Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! System is ready for development.")
        print("\n🚀 Next steps:")
        print("   1. Run: python src/main.py")
        print("   2. Test with webcam or video file")
        print("   3. Begin Week 2 development (LoG integration)")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
