#!/usr/bin/env python3
"""
Debug script to see exactly what YOLO is detecting in our videos
"""

import cv2
import numpy as np
from ultralytics import YOLO
import supervision as sv

def debug_yolo_detections(video_path: str, max_frames: int = 10):
    """
    Debug what YOLO actually detects in the video
    """
    
    print(f"🔍 Debugging YOLO detections in: {video_path}")
    
    # Load YOLO model
    model = YOLO("yolov8n.pt")
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"❌ Could not open video: {video_path}")
        return
    
    # Get COCO class names
    class_names = model.names  # This gives us the COCO class names
    
    print(f"📋 YOLO Model Classes Available:")
    for class_id, class_name in class_names.items():
        print(f"   {class_id}: {class_name}")
    
    print(f"\n🎬 Processing {max_frames} frames...")
    
    frame_count = 0
    all_detections = {}
    
    while frame_count < max_frames:
        ret, frame = cap.read()
        if not ret:
            break
        
        print(f"\n--- Frame {frame_count} ---")
        
        # Run YOLO with very low confidence to see everything
        results = model(frame, conf=0.05, verbose=False)[0]
        
        # Convert to supervision format
        detections = sv.Detections.from_ultralytics(results)
        
        print(f"Raw detections: {len(detections)} objects")
        
        # Group by class
        frame_detections = {}
        for i in range(len(detections)):
            class_id = detections.class_id[i]
            confidence = detections.confidence[i]
            class_name = class_names.get(class_id, f"unknown_{class_id}")
            
            if class_name not in frame_detections:
                frame_detections[class_name] = []
            frame_detections[class_name].append(confidence)
        
        # Print detections for this frame
        for class_name, confidences in frame_detections.items():
            max_conf = max(confidences)
            count = len(confidences)
            print(f"   {class_name}: {count} detections (max conf: {max_conf:.3f})")
            
            # Add to overall statistics
            if class_name not in all_detections:
                all_detections[class_name] = []
            all_detections[class_name].extend(confidences)
        
        frame_count += 1
    
    cap.release()
    
    # Print overall statistics
    print(f"\n📊 OVERALL DETECTION STATISTICS:")
    print(f"Processed {frame_count} frames")
    print(f"Classes detected across all frames:")
    
    for class_name, confidences in sorted(all_detections.items()):
        total_detections = len(confidences)
        max_conf = max(confidences)
        avg_conf = np.mean(confidences)
        print(f"   {class_name}: {total_detections} total detections")
        print(f"      - Max confidence: {max_conf:.3f}")
        print(f"      - Avg confidence: {avg_conf:.3f}")
        print(f"      - Frames with this class: {total_detections // max(1, total_detections // frame_count)}")

def main():
    """Debug both videos"""
    
    print("🔍 YOLO DETECTION DEBUGGING")
    print("=" * 50)
    
    # Debug Video 1
    print("\n🎬 VIDEO 1: nachine-human.mp4 (Humans Only)")
    print("-" * 40)
    debug_yolo_detections("data/test_videos/nachine-human.mp4", max_frames=5)
    
    print("\n" + "="*50)
    
    # Debug Video 2  
    print("\n🎬 VIDEO 2: test2.mp4 (Machine and Human)")
    print("-" * 40)
    debug_yolo_detections("data/test_videos/test2.mp4", max_frames=5)
    
    print("\n" + "="*50)
    print("🎯 ANALYSIS COMPLETE")
    print("This shows us exactly what YOLO can detect in our videos")
    print("We can then adjust our class mapping and confidence thresholds accordingly")

if __name__ == "__main__":
    main()
