# Person-Under-Load Safety System Configuration

# Detection Settings
detection:
  model_path: "yolov8n.pt"  # Will download automatically if not present
  confidence_threshold: 0.3
  safety_classes:
    person: 0
    car: 2
    motorcycle: 3
    bus: 5
    truck: 7
    crane: 80      # Custom class (requires custom model)
    suspended_load: 81  # Custom class (requires custom model)
    scaffolding: 82     # Custom class (requires custom model)

# Tracking Settings
tracking:
  max_disappeared: 30      # Frames before object considered lost
  max_distance: 100        # Maximum distance for object matching
  track_activation_threshold: 0.25
  lost_track_buffer: 30
  minimum_matching_threshold: 0.8
  frame_rate: 30
  trajectory_length: 60    # Frames to keep in trajectory history

# Safety Assessment
safety:
  distance_thresholds:
    high_risk: 100    # pixels - will be replaced with 3D measurements
    medium_risk: 200  # pixels - will be replaced with 3D measurements
  
  priority_levels:
    person: 1           # Highest priority
    suspended_load: 1   # Highest priority
    crane: 2           # High priority
    truck: 3           # Medium priority
    scaffolding: 4     # Lower priority
    car: 5             # Lowest priority

# Visualization Settings
visualization:
  colors:
    person: [0, 255, 0]        # Green
    crane: [255, 165, 0]       # Orange
    suspended_load: [255, 0, 0] # Red
    truck: [0, 255, 255]       # Cyan
    car: [255, 255, 0]         # Yellow
    default: [128, 128, 128]   # Gray
  
  safety_colors:
    SAFE: [0, 255, 0]      # Green
    MEDIUM: [0, 255, 255]  # Yellow
    HIGH: [0, 165, 255]    # Orange
    DANGER: [0, 0, 255]    # Red
  
  font_scale: 0.6
  thickness: 2
  trajectory_max_points: 30

# Performance Settings
performance:
  target_fps: 30
  max_processing_time: 0.1  # seconds (100ms)
  
# Camera Settings (for future use)
camera:
  resolution: [1920, 1080]
  fps: 30
  
# Future: 3D Geometry Settings
geometry:
  # Camera calibration parameters (to be filled)
  camera_matrix: null
  distortion_coefficients: null
  
  # Homography settings
  ground_plane_points: null  # Will be set during calibration
  
  # Depth estimation
  depth_model: "DepthAnything-AC"  # Future implementation
  
# Logging
logging:
  level: "INFO"
  save_detections: false
  save_frames: false
  output_directory: "output/"
