import cv2
import numpy as np
from typing import List, Tuple, Optional
from dataclasses import dataclass
import supervision as sv

@dataclass
class RefinedDetection:
    """Enhanced detection with precise edges"""
    original_bbox: List[float]
    refined_bbox: List[float]
    edge_strength: float
    geometric_confidence: float
    contour_points: Optional[np.ndarray] = None
    center_refined: Optional[Tuple[float, float]] = None

class LaplacianEdgeRefiner:
    """
    Laplacian of Gaussian edge refinement for precise object boundaries
    Critical for accurate 3D mapping and fall-zone calculations
    """
    
    def __init__(self, sigma: float = 1.0, threshold: float = 0.1):
        """
        Initialize LoG edge refiner
        
        Args:
            sigma: Gaussian sigma for LoG kernel
            threshold: Edge strength threshold for refinement
        """
        self.sigma = sigma
        self.threshold = threshold
        
        # Pre-compute LoG kernel for efficiency
        self.log_kernel = self._create_log_kernel(sigma)
        
        print(f"🔍 LaplacianEdgeRefiner initialized:")
        print(f"   📐 Sigma: {sigma}")
        print(f"   🎯 Threshold: {threshold}")
        print(f"   📏 Kernel size: {self.log_kernel.shape}")
        
    def _create_log_kernel(self, sigma: float, size: int = None) -> np.ndarray:
        """
        Create Laplacian of Gaussian kernel
        
        LoG(x,y) = -1/(πσ⁴) * [1 - (x² + y²)/(2σ²)] * e^(-(x² + y²)/(2σ²))
        """
        if size is None:
            size = int(6 * sigma + 1)
            if size % 2 == 0:
                size += 1
        
        kernel = np.zeros((size, size))
        center = size // 2
        sigma_sq = sigma * sigma
        
        for i in range(size):
            for j in range(size):
                x = i - center
                y = j - center
                r_sq = x*x + y*y
                
                # LoG formula
                kernel[i, j] = (-1.0 / (np.pi * sigma_sq * sigma_sq)) * \
                              (1 - r_sq / (2 * sigma_sq)) * \
                              np.exp(-r_sq / (2 * sigma_sq))
        
        return kernel
    
    def refine_detections(self, frame: np.ndarray, detections: sv.Detections, 
                         objects_info: List[dict]) -> List[RefinedDetection]:
        """
        Refine YOLO detections using LoG edge analysis
        
        Args:
            frame: Input frame
            detections: YOLO detections
            objects_info: Object details from detector
            
        Returns:
            List of refined detections with precise boundaries
        """
        # Convert to grayscale for edge detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Apply LoG filter
        log_response = cv2.filter2D(gray, cv2.CV_64F, self.log_kernel)
        
        refined_detections = []
        
        for i, bbox in enumerate(detections.xyxy):
            obj_info = objects_info[i] if i < len(objects_info) else {}
            
            # Extract region of interest
            x1, y1, x2, y2 = bbox.astype(int)
            
            # Ensure bbox is within frame bounds
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(frame.shape[1], x2), min(frame.shape[0], y2)
            
            if x2 <= x1 or y2 <= y1:
                continue
            
            roi_gray = gray[y1:y2, x1:x2]
            roi_log = log_response[y1:y2, x1:x2]
            
            # Refine boundaries using edge information
            refined_detection = self._refine_single_detection(
                roi_gray, roi_log, bbox, obj_info
            )
            
            if refined_detection:
                refined_detections.append(refined_detection)
        
        return refined_detections
    
    def _refine_single_detection(self, roi_gray: np.ndarray, roi_log: np.ndarray, 
                               original_bbox: np.ndarray, obj_info: dict) -> Optional[RefinedDetection]:
        """Refine a single detection using LoG analysis"""
        
        # Calculate edge strength
        edge_strength = np.sum(np.abs(roi_log))
        
        # Skip if edges are too weak
        if edge_strength < self.threshold:
            return None
        
        # Find zero-crossings (edges) in LoG response
        zero_crossings = self._find_zero_crossings(roi_log)
        
        # Calculate refined bounding box
        refined_bbox = self._calculate_refined_bbox(
            zero_crossings, original_bbox, roi_log.shape
        )
        
        # Find object contour for precise boundaries
        contour_points = self._extract_contour(roi_gray, zero_crossings)

        # FIXED: Offset contour points to global image coordinates
        if contour_points is not None and len(contour_points) > 0:
            # Add bbox offset to move contour from ROI coordinates to global coordinates
            offset_x, offset_y = int(original_bbox[0]), int(original_bbox[1])
            contour_points = contour_points + np.array([offset_x, offset_y])

        # Calculate geometric confidence
        geometric_confidence = self._calculate_geometric_confidence(
            roi_log, obj_info.get('class_name', 'unknown')
        )

        # Calculate refined center
        if contour_points is not None and len(contour_points) > 0:
            moments = cv2.moments(contour_points)
            if moments['m00'] != 0:
                cx = moments['m10'] / moments['m00']
                cy = moments['m01'] / moments['m00']
                center_refined = (cx, cy)
            else:
                center_refined = None
        else:
            center_refined = None
        
        return RefinedDetection(
            original_bbox=original_bbox.tolist(),
            refined_bbox=refined_bbox.tolist(),
            edge_strength=float(edge_strength),
            geometric_confidence=float(geometric_confidence),
            contour_points=contour_points,
            center_refined=center_refined
        )

    def _find_zero_crossings(self, log_response: np.ndarray) -> np.ndarray:
        """Find zero-crossings in LoG response (edge locations)"""

        # Create binary mask for zero crossings
        zero_crossings = np.zeros_like(log_response, dtype=np.uint8)

        # Check horizontal zero crossings
        for i in range(log_response.shape[0]):
            for j in range(log_response.shape[1] - 1):
                if log_response[i, j] * log_response[i, j + 1] < 0:
                    zero_crossings[i, j] = 255

        # Check vertical zero crossings
        for i in range(log_response.shape[0] - 1):
            for j in range(log_response.shape[1]):
                if log_response[i, j] * log_response[i + 1, j] < 0:
                    zero_crossings[i, j] = 255

        return zero_crossings

    def _calculate_refined_bbox(self, zero_crossings: np.ndarray,
                              original_bbox: np.ndarray, roi_shape: Tuple) -> np.ndarray:
        """Calculate refined bounding box based on edge information"""

        # Find edge points
        edge_points = np.where(zero_crossings > 0)

        if len(edge_points[0]) == 0:
            return original_bbox

        # Calculate tight bounding box around edges
        min_y, max_y = np.min(edge_points[0]), np.max(edge_points[0])
        min_x, max_x = np.min(edge_points[1]), np.max(edge_points[1])

        # Convert back to global coordinates
        x1 = original_bbox[0] + min_x
        y1 = original_bbox[1] + min_y
        x2 = original_bbox[0] + max_x
        y2 = original_bbox[1] + max_y

        # Add small padding to avoid too tight bounds
        padding = 5
        x1 = max(original_bbox[0], x1 - padding)
        y1 = max(original_bbox[1], y1 - padding)
        x2 = min(original_bbox[2], x2 + padding)
        y2 = min(original_bbox[3], y2 + padding)

        return np.array([x1, y1, x2, y2])

    def _extract_contour(self, roi_gray: np.ndarray, zero_crossings: np.ndarray) -> Optional[np.ndarray]:
        """Extract object contour from edge information"""

        # Use Canny edge detection as backup
        edges = cv2.Canny(roi_gray, 50, 150)

        # Combine LoG zero crossings with Canny edges
        combined_edges = cv2.bitwise_or(zero_crossings, edges)

        # Find contours
        contours, _ = cv2.findContours(combined_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if contours:
            # Return largest contour
            largest_contour = max(contours, key=cv2.contourArea)
            return largest_contour

        return None

    def _calculate_geometric_confidence(self, roi_log: np.ndarray, class_name: str) -> float:
        """Calculate confidence in geometric detection based on edge patterns"""

        # Calculate edge density
        edge_density = np.sum(np.abs(roi_log) > self.threshold) / roi_log.size

        # Calculate edge coherence (how well edges form closed shapes)
        edge_variance = np.var(roi_log[np.abs(roi_log) > self.threshold]) if np.any(np.abs(roi_log) > self.threshold) else 0

        # Class-specific confidence adjustments
        class_multiplier = {
            'person': 1.0,        # People have complex edge patterns
            'suspended_load': 1.2, # Loads should have clear geometric shapes
            'crane': 0.9,         # Cranes have complex structures
            'truck': 1.1,         # Vehicles have clear edges
        }.get(class_name, 1.0)

        # Combine factors
        confidence = (edge_density * 0.7 + (1.0 / (1.0 + edge_variance)) * 0.3) * class_multiplier

        return min(1.0, max(0.0, confidence))
