import cv2
import numpy as np
from ultralytics import YOLO
import supervision as sv
from typing import List, Dict, Tuple, Optional
import time

class ConstructionSiteDetector:
    """
    YOLO-based detector optimized for construction site safety
    Focuses on: people, equipment, suspended loads
    """
    
    def __init__(self, model_path: str = "yolov8n.pt", confidence_threshold: float = 0.4):
        """
        Initialize the detector with construction site specific settings
        
        Args:
            model_path: Path to YOLO model weights
            confidence_threshold: Minimum confidence for detections
        """
        self.model = YOLO(model_path)
        self.confidence_threshold = confidence_threshold
        
        # Construction site specific class mapping
        # Based on real video analysis - mapping YOLO classes to construction safety categories
        self.safety_classes = {
            # PEOPLE (Priority 1)
            0: "person",           # Workers - primary detection target

            # VEHICLES (Priority 3-4)
            1: "bicycle",         # Small vehicles
            2: "car",             # Vehicles
            3: "motorcycle",      # Small vehicles
            5: "bus",             # Large vehicles
            7: "truck",           # Construction vehicles
            8: "boat",            # Large objects

            # POTENTIAL EQUIPMENT (Priority 2-3)
            # Based on real video analysis - <PERSON><PERSON><PERSON> misclassifies equipment as these:
            14: "equipment_bird",  # Construction equipment often detected as "bird"
            30: "equipment_skis",  # Equipment parts detected as "skis"
            24: "equipment_backpack", # Equipment detected as "backpack"
            15: "equipment_animal", # Sometimes equipment detected as animals
            16: "equipment_animal", # Sometimes equipment detected as animals

            # LARGE OBJECTS (Potential loads/equipment)
            4: "large_object",    # airplane - large objects
            6: "large_object",    # train - large objects

            # INFRASTRUCTURE
            9: "infrastructure",  # traffic light
            10: "infrastructure", # fire hydrant
            11: "infrastructure", # stop sign
            13: "infrastructure", # bench
        }
        
        # Performance tracking
        self.frame_count = 0
        self.detection_times = []
        
    def detect_objects(self, frame: np.ndarray) -> sv.Detections:
        """
        Detect objects in frame optimized for construction safety
        
        Args:
            frame: Input video frame
            
        Returns:
            supervision.Detections object with bounding boxes, confidences, class_ids
        """
        start_time = time.time()
        
        # Run YOLO inference
        results = self.model(frame, verbose=False)[0]
        
        # Convert to supervision format for easier handling
        detections = sv.Detections.from_ultralytics(results)
        
        # Filter for construction site relevant objects
        detections = self._filter_safety_relevant(detections)
        
        # Apply confidence threshold
        detections = detections[detections.confidence > self.confidence_threshold]
        
        # Track performance
        detection_time = time.time() - start_time
        self.detection_times.append(detection_time)
        self.frame_count += 1
        
        return detections
    
    def _filter_safety_relevant(self, detections: sv.Detections) -> sv.Detections:
        """Filter detections to safety-relevant objects only"""
        
        # Keep only classes relevant to construction safety
        relevant_mask = np.isin(detections.class_id, list(self.safety_classes.keys()))
        
        return detections[relevant_mask]
    
    def get_object_details(self, detections: sv.Detections) -> List[Dict]:
        """
        Extract detailed information about each detection
        
        Returns:
            List of dictionaries with object details
        """
        objects = []
        
        for i in range(len(detections)):
            bbox = detections.xyxy[i]
            confidence = detections.confidence[i]
            class_id = detections.class_id[i]
            
            # Calculate additional metrics
            center_x = (bbox[0] + bbox[2]) / 2
            center_y = (bbox[1] + bbox[3]) / 2
            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]
            area = width * height
            
            object_info = {
                'id': i,  # Will be replaced by tracker ID later
                'class_id': class_id,
                'class_name': self.safety_classes.get(class_id, f"class_{class_id}"),
                'confidence': float(confidence),
                'bbox': bbox.tolist(),
                'center': (float(center_x), float(center_y)),
                'dimensions': (float(width), float(height)),
                'area': float(area),
                'safety_priority': self._get_safety_priority(class_id),
            }
            
            objects.append(object_info)
        
        return objects
    
    def _get_safety_priority(self, class_id: int) -> int:
        """
        Assign safety priority levels (1=highest, 5=lowest)
        Based on real video analysis and construction safety importance

        Args:
            class_id: YOLO class ID

        Returns:
            Priority level for safety assessment
        """
        priority_map = {
            # HIGHEST PRIORITY - People
            0: 1,    # person - highest priority (workers)

            # HIGH PRIORITY - Potential equipment/loads
            14: 2,   # equipment_bird (construction equipment)
            30: 2,   # equipment_skis (equipment parts)
            24: 2,   # equipment_backpack (equipment)
            15: 2,   # equipment_animal (misclassified equipment)
            16: 2,   # equipment_animal (misclassified equipment)

            # MEDIUM PRIORITY - Large objects and vehicles
            4: 3,    # large_object (airplane - could be crane)
            6: 3,    # large_object (train - large equipment)
            7: 3,    # truck - construction vehicles
            5: 3,    # bus - large vehicles
            8: 3,    # boat - large objects

            # LOWER PRIORITY - Small vehicles
            1: 4,    # bicycle
            2: 4,    # car
            3: 4,    # motorcycle

            # LOWEST PRIORITY - Infrastructure
            9: 5,    # infrastructure (traffic light)
            10: 5,   # infrastructure (fire hydrant)
            11: 5,   # infrastructure (stop sign)
            13: 5,   # infrastructure (bench)
        }

        return priority_map.get(class_id, 5)
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        if not self.detection_times:
            return {
                'frames_processed': 0,
                'avg_detection_time': 0,
                'estimated_fps': 0,
                'total_detections': 0,
            }

        avg_time = np.mean(self.detection_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0

        return {
            'frames_processed': self.frame_count,
            'avg_detection_time': float(avg_time),
            'estimated_fps': float(fps),
            'total_detections': self.frame_count,  # Fixed: each frame has detections, not each time
        }
