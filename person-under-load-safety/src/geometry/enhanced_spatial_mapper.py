"""
Enhanced Spatial Mapper with DepthAnything-AC Integration
Week 3 Enhancement: Precision & Speed Optimization

Combines geometric projection with robust depth estimation
Implements Kalman filtering for temporal smoothing
"""

import numpy as np
import cv2
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
from .depth_anything_integration import DepthAnythingACIntegration, DepthEstimationResult
from .coordinate_mapper import Position3D, CameraCalibration

@dataclass
class KalmanState:
    """Kalman filter state for object tracking"""
    position: np.ndarray  # [x, y, z, vx, vy, vz]
    covariance: np.ndarray
    last_update: float

class EnhancedSpatialMapper:
    """
    Enhanced spatial mapper combining geometric projection with depth estimation
    Implements temporal smoothing and improved accuracy
    """
    
    def __init__(self, camera_calibration: CameraCalibration):
        """Initialize enhanced spatial mapper"""
        self.camera_cal = camera_calibration
        
        # Initialize depth estimator
        self.depth_estimator = DepthAnythingACIntegration()
        
        # Kalman filters for temporal smoothing
        self.kalman_filters: Dict[int, KalmanState] = {}
        
        # Performance tracking
        self.processing_times = []
        self.accuracy_scores = []
        
        # Distance calibration parameters
        self.distance_scale_factor = 1.0
        self.distance_offset = 0.0
        
        print("🌐 Enhanced Spatial Mapper initialized")
        print("   ✅ DepthAnything-AC integration")
        print("   ✅ Kalman filtering for temporal smoothing")
        print("   ✅ Improved distance calibration")
    
    def map_detections_to_3d_enhanced(self, detections, frame: np.ndarray) -> List[Position3D]:
        """
        Enhanced 3D mapping with depth fusion and temporal smoothing
        
        Args:
            detections: Object detections with bounding boxes
            frame: Current video frame
            
        Returns:
            List of enhanced 3D positions
        """
        # Get robust depth map
        depth_result = self.depth_estimator.estimate_depth_robust(frame)
        
        positions_3d = []
        current_time = cv2.getTickCount() / cv2.getTickFrequency()
        
        for i, detection in enumerate(detections):
            # Get detection center point from refined bbox
            if hasattr(detection, 'refined_bbox'):
                # Week 3: Use refined bbox from LoG edge refinement
                bbox = detection.refined_bbox
                center_x = int(bbox[0] + bbox[2] / 2)  # x + width/2
                center_y = int(bbox[1] + bbox[3] / 2)  # y + height/2
            elif hasattr(detection, 'original_bbox'):
                # Fallback to original bbox
                bbox = detection.original_bbox
                center_x = int(bbox[0] + bbox[2] / 2)
                center_y = int(bbox[1] + bbox[3] / 2)
            else:
                # Skip if no bbox available
                continue

            # Bounds checking for image coordinates
            height, width = frame.shape[:2]
            center_x = max(0, min(center_x, width - 1))
            center_y = max(0, min(center_y, height - 1))
            
            # Method 1: Depth-based 3D conversion
            depth_position = self._convert_with_depth(
                depth_result.depth_map, (center_x, center_y)
            )
            
            # Method 2: Geometric projection (fallback)
            geometric_position = self._convert_with_geometry((center_x, center_y))
            
            # Fuse depth and geometric estimates
            fused_position = self._fuse_depth_geometry(depth_position, geometric_position, 
                                                     depth_result.confidence_map[center_y, center_x])
            
            if fused_position is not None:
                # Apply Kalman filtering for temporal smoothing
                object_id = getattr(detection, 'id', i)  # Use detection ID or index
                smoothed_position = self._apply_kalman_smoothing(
                    object_id,
                    fused_position,
                    current_time
                )
                
                # Apply distance calibration
                calibrated_position = self._apply_distance_calibration(smoothed_position)
                
                positions_3d.append(calibrated_position)
        
        # Update performance metrics
        self.processing_times.append(depth_result.processing_time)
        self.accuracy_scores.append(depth_result.spatial_consistency_score)
        
        return positions_3d
    
    def _convert_with_depth(self, depth_map: np.ndarray, pixel_coords: Tuple[int, int]) -> Optional[Position3D]:
        """Convert using depth map"""
        # Create camera matrix from calibration
        camera_matrix = np.array([
            [self.camera_cal.focal_length, 0, self.camera_cal.principal_point[0]],
            [0, self.camera_cal.focal_length, self.camera_cal.principal_point[1]],
            [0, 0, 1]
        ])
        
        world_coords = self.depth_estimator.convert_depth_to_3d(
            depth_map, pixel_coords, camera_matrix
        )

        if world_coords is not None:
            return Position3D(
                x=world_coords[0],
                y=world_coords[1],
                z=world_coords[2],
                confidence=0.8,  # High confidence for depth-based estimation
                pixel_coords=pixel_coords
            )

        return None
    
    def _convert_with_geometry(self, pixel_coords: Tuple[int, int]) -> Optional[Position3D]:
        """Convert using geometric projection (fallback method)"""
        x_pixel, y_pixel = pixel_coords
        
        # Convert pixel to normalized coordinates
        x_norm = (x_pixel - self.camera_cal.principal_point[0]) / self.camera_cal.focal_length
        y_norm = (y_pixel - self.camera_cal.principal_point[1]) / self.camera_cal.focal_length
        
        # Apply camera tilt
        cos_tilt = np.cos(self.camera_cal.tilt_angle)
        sin_tilt = np.sin(self.camera_cal.tilt_angle)
        
        # Project to ground plane
        if abs(y_norm * cos_tilt + sin_tilt) < 1e-6:
            return None
        
        scale = self.camera_cal.height_above_ground / (y_norm * cos_tilt + sin_tilt)
        
        if scale <= 0:
            return None
        
        # Calculate world coordinates
        x_world = x_norm * scale
        y_world = scale * cos_tilt - self.camera_cal.height_above_ground * sin_tilt
        z_world = 0.0  # Assume ground level

        return Position3D(
            x=x_world,
            y=y_world,
            z=z_world,
            confidence=0.6,  # Medium confidence for geometric estimation
            pixel_coords=pixel_coords
        )
    
    def _fuse_depth_geometry(self, depth_pos: Optional[Position3D], 
                           geo_pos: Optional[Position3D], 
                           confidence: float) -> Optional[Position3D]:
        """Fuse depth-based and geometric estimates"""
        if depth_pos is None and geo_pos is None:
            return None
        
        if depth_pos is None:
            return geo_pos
        
        if geo_pos is None:
            return depth_pos
        
        # Weight based on depth confidence
        depth_weight = confidence
        geo_weight = 1.0 - confidence
        
        # Normalize weights
        total_weight = depth_weight + geo_weight
        depth_weight /= total_weight
        geo_weight /= total_weight
        
        # Weighted fusion
        fused_x = depth_weight * depth_pos.x + geo_weight * geo_pos.x
        fused_y = depth_weight * depth_pos.y + geo_weight * geo_pos.y
        fused_z = depth_weight * depth_pos.z + geo_weight * geo_pos.z

        # Use higher confidence position's pixel coords
        pixel_coords = depth_pos.pixel_coords if depth_weight > geo_weight else geo_pos.pixel_coords
        fused_confidence = depth_weight * depth_pos.confidence + geo_weight * geo_pos.confidence

        return Position3D(
            x=fused_x,
            y=fused_y,
            z=fused_z,
            confidence=fused_confidence,
            pixel_coords=pixel_coords
        )
    
    def _apply_kalman_smoothing(self, object_id: int, position: Position3D, 
                              current_time: float) -> Position3D:
        """Apply Kalman filtering for temporal smoothing"""
        
        if object_id not in self.kalman_filters:
            # Initialize new Kalman filter
            self._initialize_kalman_filter(object_id, position, current_time)
            return position
        
        kalman_state = self.kalman_filters[object_id]
        dt = current_time - kalman_state.last_update
        
        # Prediction step
        F = self._get_transition_matrix(dt)
        predicted_state = F @ kalman_state.position
        predicted_covariance = F @ kalman_state.covariance @ F.T + self._get_process_noise(dt)
        
        # Update step
        measurement = np.array([position.x, position.y, position.z, 0, 0, 0])
        H = np.eye(6)  # Observation matrix
        R = np.diag([0.5, 0.5, 0.5, 10, 10, 10])  # Measurement noise
        
        # Kalman gain
        S = H @ predicted_covariance @ H.T + R
        K = predicted_covariance @ H.T @ np.linalg.inv(S)
        
        # Update state
        updated_state = predicted_state + K @ (measurement - H @ predicted_state)
        updated_covariance = (np.eye(6) - K @ H) @ predicted_covariance
        
        # Store updated state
        kalman_state.position = updated_state
        kalman_state.covariance = updated_covariance
        kalman_state.last_update = current_time

        return Position3D(
            x=updated_state[0],
            y=updated_state[1],
            z=updated_state[2],
            confidence=position.confidence,  # Maintain original confidence
            pixel_coords=position.pixel_coords
        )
    
    def _initialize_kalman_filter(self, object_id: int, position: Position3D, current_time: float):
        """Initialize Kalman filter for new object"""
        initial_state = np.array([position.x, position.y, position.z, 0, 0, 0])
        initial_covariance = np.diag([1.0, 1.0, 1.0, 5.0, 5.0, 5.0])
        
        self.kalman_filters[object_id] = KalmanState(
            position=initial_state,
            covariance=initial_covariance,
            last_update=current_time
        )
    
    def _get_transition_matrix(self, dt: float) -> np.ndarray:
        """Get state transition matrix for constant velocity model"""
        F = np.eye(6)
        F[0, 3] = dt  # x += vx * dt
        F[1, 4] = dt  # y += vy * dt
        F[2, 5] = dt  # z += vz * dt
        return F
    
    def _get_process_noise(self, dt: float) -> np.ndarray:
        """Get process noise covariance matrix"""
        # Acceleration noise
        sigma_a = 0.5  # m/s²
        
        Q = np.zeros((6, 6))
        
        # Position noise
        Q[0, 0] = Q[1, 1] = Q[2, 2] = (sigma_a * dt**2 / 2)**2
        
        # Velocity noise
        Q[3, 3] = Q[4, 4] = Q[5, 5] = (sigma_a * dt)**2
        
        # Position-velocity correlation
        Q[0, 3] = Q[3, 0] = sigma_a**2 * dt**3 / 2
        Q[1, 4] = Q[4, 1] = sigma_a**2 * dt**3 / 2
        Q[2, 5] = Q[5, 2] = sigma_a**2 * dt**3 / 2
        
        return Q
    
    def _apply_distance_calibration(self, position: Position3D) -> Position3D:
        """Apply distance calibration to improve accuracy"""
        # Calculate distance from camera
        distance = np.sqrt(position.x**2 + position.y**2 + position.z**2)
        
        # Apply calibration
        calibrated_distance = distance * self.distance_scale_factor + self.distance_offset
        
        # Scale position proportionally
        if distance > 0:
            scale_factor = calibrated_distance / distance
            return Position3D(
                x=position.x * scale_factor,
                y=position.y * scale_factor,
                z=position.z * scale_factor,
                confidence=position.confidence,
                pixel_coords=position.pixel_coords
            )

        return position
    
    def update_calibration(self, ground_truth_distances: List[float], 
                          measured_distances: List[float]):
        """Update distance calibration based on ground truth"""
        if len(ground_truth_distances) != len(measured_distances) or len(ground_truth_distances) < 2:
            return
        
        # Linear regression to find scale factor and offset
        X = np.column_stack([measured_distances, np.ones(len(measured_distances))])
        y = np.array(ground_truth_distances)
        
        # Solve least squares
        coeffs = np.linalg.lstsq(X, y, rcond=None)[0]
        self.distance_scale_factor = coeffs[0]
        self.distance_offset = coeffs[1]
        
        print(f"📐 Distance calibration updated:")
        print(f"   Scale factor: {self.distance_scale_factor:.3f}")
        print(f"   Offset: {self.distance_offset:.3f}m")
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        if not self.processing_times:
            return {}
        
        return {
            'avg_processing_time': np.mean(self.processing_times),
            'avg_accuracy_score': np.mean(self.accuracy_scores),
            'active_kalman_filters': len(self.kalman_filters),
            'distance_scale_factor': self.distance_scale_factor,
            'distance_offset': self.distance_offset
        }
    
    def calculate_distance_3d(self, pos1: Position3D, pos2: Position3D) -> float:
        """Calculate 3D Euclidean distance between two positions"""
        return np.sqrt((pos1.x - pos2.x)**2 + (pos1.y - pos2.y)**2 + (pos1.z - pos2.z)**2)
