import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
import supervision as sv

@dataclass
class TrackedObject:
    """Data structure for tracked objects"""
    id: int
    class_id: int
    class_name: str
    bbox: List[float]
    center: Tuple[float, float]
    confidence: float
    safety_priority: int
    velocity: Optional[Tuple[float, float]] = None
    trajectory: Optional[deque] = None
    last_seen: int = 0
    frames_tracked: int = 0

class SafetyTracker:
    """
    Multi-object tracker optimized for construction site safety
    Uses ByteTrack algorithm with safety-specific enhancements
    """
    
    def __init__(self, max_disappeared: int = 30, max_distance: float = 100):
        """
        Initialize tracker
        
        Args:
            max_disappeared: Frames before considering object lost
            max_distance: Maximum distance for object matching
        """
        self.max_disappeared = max_disappeared
        self.max_distance = max_distance
        
        # Tracking state
        self.next_id = 0
        self.tracked_objects: Dict[int, TrackedObject] = {}
        self.frame_count = 0
        
        # ByteTrack-style tracker
        self.byte_tracker = sv.ByteTrack(
            track_activation_threshold=0.15,  # Lowered to catch equipment detections
            lost_track_buffer=30,
            minimum_matching_threshold=0.7,   # Lowered for more flexible matching
            frame_rate=30
        )
        
        # Safety-specific tracking
        self.worker_trajectories = defaultdict(lambda: deque(maxlen=60))  # 2 seconds at 30fps
        self.load_positions = defaultdict(lambda: deque(maxlen=10))       # Short history for loads
        
    def update(self, detections: sv.Detections, objects_info: List[Dict]) -> List[TrackedObject]:
        """
        Update tracker with new detections
        
        Args:
            detections: Current frame detections
            objects_info: Detailed object information
            
        Returns:
            List of tracked objects with IDs
        """
        self.frame_count += 1
        
        # Update ByteTracker
        tracked_detections = self.byte_tracker.update_with_detections(detections)
        
        # Convert to our TrackedObject format
        tracked_objects = []
        
        for i, track_id in enumerate(tracked_detections.tracker_id):
            if track_id is None:
                continue
                
            obj_info = objects_info[i] if i < len(objects_info) else {}
            
            tracked_obj = TrackedObject(
                id=int(track_id),
                class_id=obj_info.get('class_id', -1),
                class_name=obj_info.get('class_name', 'unknown'),
                bbox=tracked_detections.xyxy[i].tolist(),
                center=obj_info.get('center', (0, 0)),
                confidence=tracked_detections.confidence[i],
                safety_priority=obj_info.get('safety_priority', 5),
                last_seen=self.frame_count,
            )
            
            # Update trajectory and velocity
            self._update_trajectory(tracked_obj)
            
            tracked_objects.append(tracked_obj)
            self.tracked_objects[tracked_obj.id] = tracked_obj
        
        # Clean up lost objects
        self._cleanup_lost_objects()
        
        return tracked_objects
    
    def _update_trajectory(self, obj: TrackedObject):
        """Update object trajectory and calculate velocity"""
        
        # Initialize trajectory if new object
        if obj.id not in self.worker_trajectories:
            obj.trajectory = deque(maxlen=60)
            obj.frames_tracked = 1
        else:
            obj.trajectory = self.worker_trajectories[obj.id]
            obj.frames_tracked += 1
        
        # Add current position
        obj.trajectory.append((obj.center, self.frame_count))
        
        # Calculate velocity (pixels per frame)
        if len(obj.trajectory) >= 2:
            current_pos, current_frame = obj.trajectory[-1]
            prev_pos, prev_frame = obj.trajectory[-2]
            
            if current_frame != prev_frame:
                dt = current_frame - prev_frame
                dx = current_pos[0] - prev_pos[0]
                dy = current_pos[1] - prev_pos[1]
                
                obj.velocity = (dx / dt, dy / dt)
        
        # Update storage
        self.worker_trajectories[obj.id] = obj.trajectory
    
    def _cleanup_lost_objects(self):
        """Remove objects that haven't been seen for too long"""
        
        lost_ids = []
        for obj_id, obj in self.tracked_objects.items():
            if self.frame_count - obj.last_seen > self.max_disappeared:
                lost_ids.append(obj_id)
        
        for obj_id in lost_ids:
            del self.tracked_objects[obj_id]
            if obj_id in self.worker_trajectories:
                del self.worker_trajectories[obj_id]
            if obj_id in self.load_positions:
                del self.load_positions[obj_id]
    
    def get_workers(self) -> List[TrackedObject]:
        """Get all tracked workers (class_id = 0)"""
        return [obj for obj in self.tracked_objects.values() if obj.class_id == 0]
    
    def get_loads(self) -> List[TrackedObject]:
        """Get all tracked suspended loads and potential equipment"""
        equipment_classes = [
            'suspended_load', 'crane', 'equipment_bird', 'equipment_skis',
            'equipment_backpack', 'equipment_animal', 'large_object'
        ]
        return [obj for obj in self.tracked_objects.values()
                if obj.class_name in equipment_classes]
    
    def get_tracking_stats(self) -> Dict:
        """Get tracking performance statistics"""
        total_objects = len(self.tracked_objects)
        workers = len(self.get_workers())
        loads = len(self.get_loads())
        
        avg_track_length = np.mean([obj.frames_tracked for obj in self.tracked_objects.values()]) \
                          if self.tracked_objects else 0
        
        return {
            'total_tracked': total_objects,
            'workers': workers,
            'loads': loads,
            'avg_track_length': avg_track_length,
            'frame_count': self.frame_count,
        }
