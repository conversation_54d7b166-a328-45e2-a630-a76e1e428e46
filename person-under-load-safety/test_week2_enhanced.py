#!/usr/bin/env python3
"""
Week 2 Enhanced Testing: LoG + 3D Spatial Intelligence
Test the new LoG edge refinement and 3D spatial mapping capabilities
"""

import cv2
import numpy as np
from src.main import PersonUnderLoadSystem
import os
import time

def test_week2_enhancements():
    """Test Week 2 enhancements with real video"""
    
    video_path = "data/test_videos/test2.mp4"
    
    print("🚀 WEEK 2 ENHANCED TESTING")
    print("=" * 70)
    print("🔬 Testing Components:")
    print("   ✅ YOLO Detection (Week 1)")
    print("   ✅ Multi-Object Tracking (Week 1)")
    print("   🆕 LoG Edge Refinement (Week 2)")
    print("   🆕 3D Spatial Mapping (Week 2)")
    print("   🆕 Enhanced Safety Assessment (Week 2)")
    print("=" * 70)
    
    if not os.path.exists(video_path):
        print(f"❌ Video not found: {video_path}")
        return
    
    # Initialize enhanced system
    print("🚀 Initializing enhanced system...")
    system = PersonUnderLoadSystem()
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"❌ Could not open video: {video_path}")
        return
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📊 Video: {width}x{height} @ {fps:.1f} FPS, {total_frames} frames")
    
    # Create output directory
    output_dir = "week2_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Test specific frames to see enhancements
    test_frames = [0, 15, 30, 45]
    
    print(f"\n🔍 Testing enhanced processing on frames: {test_frames}")
    print("-" * 70)
    
    frame_count = 0
    enhancement_stats = {
        'total_detections': 0,
        'total_refined': 0,
        'total_3d_mapped': 0,
        'avg_edge_strength': [],
        'avg_geometric_confidence': [],
        'avg_3d_distance': [],
        'processing_times': [],
    }
    
    while frame_count <= max(test_frames):
        ret, frame = cap.read()
        if not ret:
            break
        
        if frame_count in test_frames:
            print(f"\n📸 Processing Frame {frame_count}...")
            
            start_time = time.time()
            
            # Process with enhanced system
            annotated_frame, stats = system.process_frame(frame)
            
            processing_time = time.time() - start_time
            enhancement_stats['processing_times'].append(processing_time)
            
            # Extract enhancement statistics
            detection_stats = stats['detection_stats']
            refinement_stats = stats['refinement_stats']
            spatial_stats = stats['spatial_stats']
            safety_status = stats['safety_status']
            
            # Update cumulative stats
            enhancement_stats['total_detections'] += detection_stats.get('frames_processed', 0)
            enhancement_stats['total_refined'] += refinement_stats['refined_detections']
            enhancement_stats['total_3d_mapped'] += spatial_stats['3d_positions']
            
            if refinement_stats['avg_edge_strength'] > 0:
                enhancement_stats['avg_edge_strength'].append(refinement_stats['avg_edge_strength'])
            if refinement_stats['avg_geometric_confidence'] > 0:
                enhancement_stats['avg_geometric_confidence'].append(refinement_stats['avg_geometric_confidence'])
            if spatial_stats['avg_distance_from_camera'] > 0:
                enhancement_stats['avg_3d_distance'].append(spatial_stats['avg_distance_from_camera'])
            
            # Print detailed results
            print(f"   ⏱️  Processing time: {processing_time*1000:.1f}ms")
            print(f"   🎯 YOLO detections: {safety_status['workers_detected']} workers, {safety_status['loads_detected']} loads")
            print(f"   🔍 LoG refined: {refinement_stats['refined_detections']} objects")
            print(f"   📐 3D mapped: {spatial_stats['3d_positions']} positions")
            print(f"   ⚡ Edge strength: {refinement_stats['avg_edge_strength']:.1f}")
            print(f"   🎯 Geometric confidence: {refinement_stats['avg_geometric_confidence']:.3f}")
            print(f"   📏 Avg 3D distance: {spatial_stats['avg_distance_from_camera']:.1f}m")
            print(f"   🛡️  Safety status: {safety_status['overall_status']}")
            print(f"   🌐 3D analysis: {'ACTIVE' if safety_status.get('3d_analysis_active') else 'INACTIVE'}")
            
            # Save enhanced frame
            output_filename = f"week2_frame_{frame_count:03d}_enhanced.jpg"
            output_path = os.path.join(output_dir, output_filename)
            
            # Add enhancement info overlay
            info_text = f"Week 2 Enhanced | Frame {frame_count} | LoG: {refinement_stats['refined_detections']} | 3D: {spatial_stats['3d_positions']}"
            cv2.putText(annotated_frame, info_text, (10, height - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            
            cv2.imwrite(output_path, annotated_frame)
            print(f"   💾 Saved: {output_filename}")
        
        frame_count += 1
    
    cap.release()
    
    # Calculate final statistics
    print("\n" + "=" * 70)
    print("📊 WEEK 2 ENHANCEMENT RESULTS:")
    print("=" * 70)
    
    print(f"🎯 DETECTION PERFORMANCE:")
    print(f"   - Total frames processed: {len(test_frames)}")
    print(f"   - Total objects refined: {enhancement_stats['total_refined']}")
    print(f"   - Total 3D positions: {enhancement_stats['total_3d_mapped']}")
    print(f"   - Refinement success rate: {enhancement_stats['total_refined']/len(test_frames):.1f} objects/frame")
    print(f"   - 3D mapping success rate: {enhancement_stats['total_3d_mapped']/len(test_frames):.1f} positions/frame")
    
    if enhancement_stats['avg_edge_strength']:
        print(f"\n🔍 LoG EDGE REFINEMENT:")
        print(f"   - Average edge strength: {np.mean(enhancement_stats['avg_edge_strength']):.1f}")
        print(f"   - Max edge strength: {np.max(enhancement_stats['avg_edge_strength']):.1f}")
        print(f"   - Min edge strength: {np.min(enhancement_stats['avg_edge_strength']):.1f}")
    
    if enhancement_stats['avg_geometric_confidence']:
        print(f"\n🎯 GEOMETRIC CONFIDENCE:")
        print(f"   - Average confidence: {np.mean(enhancement_stats['avg_geometric_confidence']):.3f}")
        print(f"   - Max confidence: {np.max(enhancement_stats['avg_geometric_confidence']):.3f}")
        print(f"   - Min confidence: {np.min(enhancement_stats['avg_geometric_confidence']):.3f}")
    
    if enhancement_stats['avg_3d_distance']:
        print(f"\n📏 3D SPATIAL MAPPING:")
        print(f"   - Average distance from camera: {np.mean(enhancement_stats['avg_3d_distance']):.1f}m")
        print(f"   - Max distance: {np.max(enhancement_stats['avg_3d_distance']):.1f}m")
        print(f"   - Min distance: {np.min(enhancement_stats['avg_3d_distance']):.1f}m")
    
    print(f"\n⚡ PERFORMANCE:")
    avg_processing_time = np.mean(enhancement_stats['processing_times'])
    print(f"   - Average processing time: {avg_processing_time*1000:.1f}ms")
    print(f"   - Theoretical max FPS: {1/avg_processing_time:.1f}")
    print(f"   - Performance vs Week 1: {'MAINTAINED' if avg_processing_time < 0.1 else 'SLOWER'}")
    
    print(f"\n📁 OUTPUT:")
    print(f"   - Enhanced frames saved to: {output_dir}/")
    print(f"   - Files created: {len(test_frames)} enhanced frames")
    
    # Success criteria
    success_criteria = {
        'refinement_working': enhancement_stats['total_refined'] > 0,
        '3d_mapping_working': enhancement_stats['total_3d_mapped'] > 0,
        'performance_acceptable': avg_processing_time < 0.2,  # Under 200ms
        'edge_detection_strong': np.mean(enhancement_stats['avg_edge_strength']) > 10 if enhancement_stats['avg_edge_strength'] else False,
    }
    
    print(f"\n✅ SUCCESS CRITERIA:")
    for criterion, passed in success_criteria.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   - {criterion.replace('_', ' ').title()}: {status}")
    
    overall_success = all(success_criteria.values())
    
    print(f"\n🎯 OVERALL WEEK 2 STATUS: {'🎉 SUCCESS!' if overall_success else '⚠️ NEEDS IMPROVEMENT'}")
    
    if overall_success:
        print("\n🚀 Week 2 enhancements are working perfectly!")
        print("🔬 LoG edge refinement is providing precise boundaries")
        print("🌐 3D spatial mapping is generating world coordinates")
        print("🛡️ Enhanced safety assessment is ready for real deployment")
        print("\n🛣️ Ready for Week 3: DepthAnything-AC integration!")
    else:
        print("\n🔧 Some enhancements need tuning - check the criteria above")

if __name__ == "__main__":
    test_week2_enhancements()
